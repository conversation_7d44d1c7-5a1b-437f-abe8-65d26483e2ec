﻿using Ardalis.Result.AspNetCore;
using Microsoft.AspNetCore.Mvc;
using Texan.AuthBroker.Application.DTOs;
using Texan.AuthBroker.Application.Services;

namespace Texan.AuthBroker.Presentation.Api.Extensions;

public static class EndpointRouteBuilderExtensions
{
    public static IEndpointRouteBuilder MapUserEndpoints(this IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/v1");

        group.MapGet("/", () => "AuthBroker is running!");

        group.MapPost("/login", async ([FromBody] TokenRequest body, [FromServices] IUserAppService userSvc, CancellationToken cancellationToken) =>
        {
            var token = await userSvc.LoginAsync(body, cancellationToken);
            return token.ToMinimalApiResult();
        })
        .WithName("Login")
        .Produces<TokenResponse>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status500InternalServerError);

        group.MapPost("/users", async ([FromBody] CreateUserRequest body, [FromServices] IUserAppService userSvc) =>
        {
            var result = await userSvc.CreateUserAsync(body);
            return result.ToMinimalApiResult();
        });

        group.MapPost("/roles/{roleName}", async ([FromRoute] string roleName, [FromServices] IUserAppService userSvc) =>
        {
            var result = await userSvc.CreateRoleAsync(roleName);
            return result.ToMinimalApiResult();
        });

        group.MapPost("/users/{userId}/roles/{roleName}", async ([FromRoute] string userId, [FromRoute] string roleName, [FromServices] IUserAppService userSvc) =>
        {
            var result = await userSvc.AssignRoleToUserAsync(userId, roleName);
            return result.ToMinimalApiResult();
        });

        group.MapPatch("/users/{userId}/active", async ([FromRoute] string userId, [FromQuery] bool isActive, [FromServices] IUserAppService userSvc) =>
        {
            var result = await userSvc.SetActiveAsync(userId, isActive);
            return result.ToMinimalApiResult();
        });

        group.MapPost("/users/{userId}/reset-password", async ([FromRoute] string userId, [FromQuery] string newPassword, [FromServices] IUserAppService userSvc) =>
        {
            var result = await userSvc.ResetPasswordAsync(userId, newPassword);
            return result.ToMinimalApiResult();
        });

        group.MapPost("/claims/{claimName}", async ([FromRoute] string claimName, [FromServices] IUserAppService userSvc) =>
        {
            var result = await userSvc.CreateCustomClaimAsync(claimName);
            return result.ToMinimalApiResult();
        });

        group.MapPost("/users/{userId}/claims", async ([FromRoute] string userId, [FromBody] CustomClaimRequest body, [FromServices] IUserAppService userSvc) =>
        {
            var result = await userSvc.AssignCustomClaimAsync(userId, body);
            return result.ToMinimalApiResult();
        });

        return app;
    }
}