﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{998ADE8E-A8A6-4A42-9B6A-F18812CB3F37}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "logging-service", "logging-service", "{7F58F221-5F9D-4BE6-804D-6EAB5DB38E56}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "logging-broker", "logging-broker", "{07BBB9D0-D29A-4033-903C-5C6AB31F7869}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "logging-service", "logging-service", "{C4CED1D0-768E-4346-873F-34A4BBB11EBB}"
	ProjectSection(SolutionItems) = preProject
		..\docker-compose.yaml = ..\docker-compose.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "logging-broker", "logging-broker", "{FA850D2F-C240-450F-99B2-EC4D61C81643}"
	ProjectSection(SolutionItems) = preProject
		Dockerfile = Dockerfile
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.LoggingBroker.Application", "Texan.LoggingBroker.Application\Texan.LoggingBroker.Application.csproj", "{6048B03F-203E-925A-FBAD-76A3D266ADDD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.LoggingBroker.Domain", "Texan.LoggingBroker.Domain\Texan.LoggingBroker.Domain.csproj", "{C309F71B-BC6B-3C2F-0709-574A7DE859CB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.LoggingBroker.WebApi", "Texan.LoggingBroker.WebApi\Texan.LoggingBroker.WebApi.csproj", "{510EFA71-F13A-2C2E-9146-1ED6EF9A8A1E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LoggingBrokerApi.IntegrationTests", "..\..\..\tests\logging-service\logging-broker\LoggingBrokerApi.IntegrationTests\LoggingBrokerApi.IntegrationTests.csproj", "{94B1147A-37F3-3A81-BC88-065A102076B8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LoggingBrokerApi.UnitTests", "..\..\..\tests\logging-service\logging-broker\LoggingBrokerApi.UnitTests\LoggingBrokerApi.UnitTests.csproj", "{094D2C4A-6B6C-9039-B89E-089E670BF384}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "logging-grafana", "logging-grafana", "{25630C4F-6686-4EDA-B584-D6FFFAB730B9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "logging-loki", "logging-loki", "{D5803D9C-3BD6-4350-B789-797EA1504949}"
	ProjectSection(SolutionItems) = preProject
		..\logging-loki\loki-config.yaml = ..\logging-loki\loki-config.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "provisioning", "provisioning", "{A3DBD695-2AA5-4123-B71D-31E2E29DF4E5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "datasources", "datasources", "{DBBAF46D-05E5-48A3-9FEC-8B54DC9F1A35}"
	ProjectSection(SolutionItems) = preProject
		..\logging-grafana\provisioning\datasources\datasource.yaml = ..\logging-grafana\provisioning\datasources\datasource.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{2DBD2852-35D5-4D5F-B05D-FF599002BF0A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "LoggingBroker", "LoggingBroker", "{0E64093A-6FA4-403F-A962-B23769DDAD97}"
	ProjectSection(SolutionItems) = preProject
		..\..\..\docs\LoggingBroker\Logging Broker API – Request Documentation.md = ..\..\..\docs\LoggingBroker\Logging Broker API – Request Documentation.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Postman", "Postman", "{3D3F2429-AA8A-4646-ACFA-5E1E5F155C8C}"
	ProjectSection(SolutionItems) = preProject
		..\..\..\docs\LoggingBroker\Postman\LoggingBrokerApi.postman_collection.json = ..\..\..\docs\LoggingBroker\Postman\LoggingBrokerApi.postman_collection.json
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.LoggingBroker.Infrastructure", "Texan.LoggingBroker.Infrastructure\Texan.LoggingBroker.Infrastructure.csproj", "{A68299E9-6FA6-4EC1-B8A2-70A3249D456B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LoggingBroker.Client", "..\LoggingBroker.Client\LoggingBroker.Client.csproj", "{9703043C-4A70-498B-B5A2-44560807E196}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestClientLibraryWebApi", "..\TestClientLibraryWebApi\TestClientLibraryWebApi.csproj", "{D406B06D-CFC8-469F-B46D-00BB6FA5E726}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "logging-broker-client", "logging-broker-client", "{89FEA58D-7AAD-442E-BA16-51EB78C2447B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LoggingBroker.Client.UnitTests", "..\..\..\tests\logging-service\logging-broker-client\LoggingBroker.Client.UnitTests\LoggingBroker.Client.UnitTests.csproj", "{5ED1A18C-388C-4746-A7A2-6CA5B91C7CB2}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6048B03F-203E-925A-FBAD-76A3D266ADDD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6048B03F-203E-925A-FBAD-76A3D266ADDD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6048B03F-203E-925A-FBAD-76A3D266ADDD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6048B03F-203E-925A-FBAD-76A3D266ADDD}.Release|Any CPU.Build.0 = Release|Any CPU
		{C309F71B-BC6B-3C2F-0709-574A7DE859CB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C309F71B-BC6B-3C2F-0709-574A7DE859CB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C309F71B-BC6B-3C2F-0709-574A7DE859CB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C309F71B-BC6B-3C2F-0709-574A7DE859CB}.Release|Any CPU.Build.0 = Release|Any CPU
		{510EFA71-F13A-2C2E-9146-1ED6EF9A8A1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{510EFA71-F13A-2C2E-9146-1ED6EF9A8A1E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{510EFA71-F13A-2C2E-9146-1ED6EF9A8A1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{510EFA71-F13A-2C2E-9146-1ED6EF9A8A1E}.Release|Any CPU.Build.0 = Release|Any CPU
		{94B1147A-37F3-3A81-BC88-065A102076B8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94B1147A-37F3-3A81-BC88-065A102076B8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94B1147A-37F3-3A81-BC88-065A102076B8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94B1147A-37F3-3A81-BC88-065A102076B8}.Release|Any CPU.Build.0 = Release|Any CPU
		{094D2C4A-6B6C-9039-B89E-089E670BF384}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{094D2C4A-6B6C-9039-B89E-089E670BF384}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{094D2C4A-6B6C-9039-B89E-089E670BF384}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{094D2C4A-6B6C-9039-B89E-089E670BF384}.Release|Any CPU.Build.0 = Release|Any CPU
		{A68299E9-6FA6-4EC1-B8A2-70A3249D456B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A68299E9-6FA6-4EC1-B8A2-70A3249D456B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A68299E9-6FA6-4EC1-B8A2-70A3249D456B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A68299E9-6FA6-4EC1-B8A2-70A3249D456B}.Release|Any CPU.Build.0 = Release|Any CPU
		{9703043C-4A70-498B-B5A2-44560807E196}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9703043C-4A70-498B-B5A2-44560807E196}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9703043C-4A70-498B-B5A2-44560807E196}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9703043C-4A70-498B-B5A2-44560807E196}.Release|Any CPU.Build.0 = Release|Any CPU
		{D406B06D-CFC8-469F-B46D-00BB6FA5E726}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D406B06D-CFC8-469F-B46D-00BB6FA5E726}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D406B06D-CFC8-469F-B46D-00BB6FA5E726}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D406B06D-CFC8-469F-B46D-00BB6FA5E726}.Release|Any CPU.Build.0 = Release|Any CPU
		{5ED1A18C-388C-4746-A7A2-6CA5B91C7CB2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5ED1A18C-388C-4746-A7A2-6CA5B91C7CB2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5ED1A18C-388C-4746-A7A2-6CA5B91C7CB2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5ED1A18C-388C-4746-A7A2-6CA5B91C7CB2}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7F58F221-5F9D-4BE6-804D-6EAB5DB38E56} = {998ADE8E-A8A6-4A42-9B6A-F18812CB3F37}
		{07BBB9D0-D29A-4033-903C-5C6AB31F7869} = {7F58F221-5F9D-4BE6-804D-6EAB5DB38E56}
		{C4CED1D0-768E-4346-873F-34A4BBB11EBB} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{FA850D2F-C240-450F-99B2-EC4D61C81643} = {C4CED1D0-768E-4346-873F-34A4BBB11EBB}
		{6048B03F-203E-925A-FBAD-76A3D266ADDD} = {FA850D2F-C240-450F-99B2-EC4D61C81643}
		{C309F71B-BC6B-3C2F-0709-574A7DE859CB} = {FA850D2F-C240-450F-99B2-EC4D61C81643}
		{510EFA71-F13A-2C2E-9146-1ED6EF9A8A1E} = {FA850D2F-C240-450F-99B2-EC4D61C81643}
		{94B1147A-37F3-3A81-BC88-065A102076B8} = {07BBB9D0-D29A-4033-903C-5C6AB31F7869}
		{094D2C4A-6B6C-9039-B89E-089E670BF384} = {07BBB9D0-D29A-4033-903C-5C6AB31F7869}
		{25630C4F-6686-4EDA-B584-D6FFFAB730B9} = {C4CED1D0-768E-4346-873F-34A4BBB11EBB}
		{D5803D9C-3BD6-4350-B789-797EA1504949} = {C4CED1D0-768E-4346-873F-34A4BBB11EBB}
		{A3DBD695-2AA5-4123-B71D-31E2E29DF4E5} = {25630C4F-6686-4EDA-B584-D6FFFAB730B9}
		{DBBAF46D-05E5-48A3-9FEC-8B54DC9F1A35} = {A3DBD695-2AA5-4123-B71D-31E2E29DF4E5}
		{0E64093A-6FA4-403F-A962-B23769DDAD97} = {2DBD2852-35D5-4D5F-B05D-FF599002BF0A}
		{3D3F2429-AA8A-4646-ACFA-5E1E5F155C8C} = {0E64093A-6FA4-403F-A962-B23769DDAD97}
		{A68299E9-6FA6-4EC1-B8A2-70A3249D456B} = {FA850D2F-C240-450F-99B2-EC4D61C81643}
		{9703043C-4A70-498B-B5A2-44560807E196} = {C4CED1D0-768E-4346-873F-34A4BBB11EBB}
		{D406B06D-CFC8-469F-B46D-00BB6FA5E726} = {C4CED1D0-768E-4346-873F-34A4BBB11EBB}
		{89FEA58D-7AAD-442E-BA16-51EB78C2447B} = {7F58F221-5F9D-4BE6-804D-6EAB5DB38E56}
		{5ED1A18C-388C-4746-A7A2-6CA5B91C7CB2} = {89FEA58D-7AAD-442E-BA16-51EB78C2447B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A914EC73-05A9-4CD0-9819-F50A1EAD3DAE}
	EndGlobalSection
EndGlobal
