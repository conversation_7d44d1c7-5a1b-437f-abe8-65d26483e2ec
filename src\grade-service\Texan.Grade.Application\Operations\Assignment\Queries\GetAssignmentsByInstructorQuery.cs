namespace Texan.Grade.Application.Operations.Assignment.Queries;

public class GetAssignmentsByInstructorQuery
{
    public class Request : IRequest<Result<List<AssignmentEntity>>>
    {
        public string CreatedByUserId { get; set; } = string.Empty;
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.CreatedByUserId)
                .NotEmpty().WithMessage("Instructor ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<List<AssignmentEntity>>>
    {
        public async Task<Result<List<AssignmentEntity>>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Getting assignments for instructor: {InstructorId}", request.CreatedByUserId);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Assignment query failed validation for instructor: {InstructorId}", request.CreatedByUserId);
                return validationError;
            }

            var assignments = await crudService
                .Query<AssignmentEntity>()
                .Where(a => a.CreatedByUserId == request.CreatedByUserId)
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync(cancellationToken);

            logger.LogInformation("Retrieved {Count} assignments for instructor: {InstructorId}", assignments.Count, request.CreatedByUserId);
            return Result.Success(assignments);
        }
    }
}