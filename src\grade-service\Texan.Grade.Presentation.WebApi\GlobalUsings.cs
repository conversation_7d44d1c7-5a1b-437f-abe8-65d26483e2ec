global using MediatR;
global using Microsoft.AspNetCore.Mvc;
global using Texan.Grade.Domain.Entities;
global using Texan.Grade.Application.Operations.Grade.Commands;
global using Texan.Grade.Application.Operations.Grade.Queries;
global using Texan.Grade.Application.Operations.Assignment.Commands;
global using Texan.Grade.Application.Operations.Assignment.Queries;
global using Texan.Grade.Application.Operations.AssignmentAttachments.Commands;
global using Texan.Grade.Application.Operations.AssignmentAttachments.Queries;
global using FluentValidation;
global using Microsoft.AspNetCore.Authentication.JwtBearer;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.IdentityModel.Tokens;
global using Texan.Grade.Application.Abstractions;
global using Texan.Grade.Infrastructure.Data;
global using Texan.Grade.Infrastructure.Persistency;
global using Texan.Grade.Presentation.WebApi.Endpoints;
global using Texan.Grade.Infrastructure.HealthChecks;