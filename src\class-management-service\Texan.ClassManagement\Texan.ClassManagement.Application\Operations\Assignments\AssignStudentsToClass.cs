﻿using Ardalis.Result;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Domain.Entities;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Application.Operations.Assignments
{
    public class AssignStudentsToClass
    {
        public class Request : IRequest<Result<List<string>>>
        {
            public string CourseClassId { get; set; } = null!;
            public List<string> StudentUserIds { get; set; } = [];
        }

        public class RequestValidator : AbstractValidator<Request>
        {
            public RequestValidator()
            {
                RuleFor(r => r.CourseClassId)
                    .NotEmpty().WithMessage("CourseClassId is required.");
                RuleFor(r => r.StudentUserIds)
                    .NotEmpty().WithMessage("StudentUserIds list cannot be empty.");
            }
        }

        public class Handler(ICrudService crudService, IValidator<Request> validator) : IRequestHandler<Request, Result<List<string>>>
        {
            public async Task<Result<List<string>>> Handle(Request request, CancellationToken cancellationToken)
            {
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = validationResult.Errors
                        .Select(x => new ValidationError
                        {
                            Identifier = x.PropertyName,
                            ErrorMessage = x.ErrorMessage
                        }).ToList();

                    return Result.Invalid(validationErrors);
                }

                var courseClass = await crudService.GetByIdAsync<CourseClassEntity>(request.CourseClassId, cancellationToken);

                if (courseClass is null)
                {
                    return Result.NotFound("Class not found.");
                }

                var participations = await crudService.GetAll<ClassParticipationEntity>()
                    .Where(p => request.StudentUserIds.Contains(p.UserId)
                        && p.CourseClassId == request.CourseClassId
                        && p.ClassParticipationType == ClassParticipationType.Student)
                    .GroupBy(p => p.UserId)
                    .Select(g => g.OrderByDescending(x => x.OperationDate).First())
                    .ToListAsync(cancellationToken);

                var userIdsWithLastJoin = participations
                    .Where(p => p.ClassOperationType == ClassOperationType.Join)
                    .Select(p => p.UserId)
                    .ToHashSet();

                var studentsToAdd = request.StudentUserIds
                    .Where(id => !userIdsWithLastJoin.Contains(id))
                    .ToList();

                var addedParticipationIds = new List<string>();

                foreach (var studentUserId in studentsToAdd)
                {
                    var participation = new ClassParticipationEntity
                    {
                        CourseClassId = request.CourseClassId,
                        UserId = studentUserId,
                        ClassOperationType = ClassOperationType.Join,
                        ClassParticipationType = ClassParticipationType.Student,
                        OperationDate = DateTime.UtcNow
                    };

                    await crudService.AddAsync(participation, cancellationToken);

                    addedParticipationIds.Add(participation.Id);
                }

                return Result.Success(addedParticipationIds);
            }
        }
    }
}