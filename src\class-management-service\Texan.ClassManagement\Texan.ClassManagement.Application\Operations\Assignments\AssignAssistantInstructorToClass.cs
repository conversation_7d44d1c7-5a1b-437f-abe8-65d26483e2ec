﻿using Ardalis.Result;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Application.Operations.RoleCheck;
using Texan.ClassManagement.Domain.Entities;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Application.Operations.Assignments
{
    public class AssignAssistantInstructorToClass
    {
        public class Request : IRequest<Result<string>>
        {
            public string CourseClassId { get; set; } = null!;
            public string AssistantInstructorUserId { get; set; } = null!;
            public ClassOperationType ClassOperationType { get; set; } = ClassOperationType.Join;
        }

        public class RequestValidator : AbstractValidator<Request>
        {
            public RequestValidator()
            {
                RuleFor(r => r.CourseClassId)
                    .NotEmpty().WithMessage("CourseClassId is required.");
                RuleFor(r => r.AssistantInstructorUserId)
                    .NotEmpty().WithMessage("AssistantInstructorUserId is required.");
            }
        }

        public class Handler(ICrudService crudService, IValidator<Request> validator, IMediator mediator) : IRequestHandler<Request, Result<string>>
        {
            public async Task<Result<string>> Handle(Request request, CancellationToken cancellationToken)
            {
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = validationResult.Errors
                        .Select(x => new ValidationError
                        {
                            Identifier = x.PropertyName,
                            ErrorMessage = x.ErrorMessage
                        }).ToList();

                    return Result.Invalid(validationErrors);
                }

                var courseClass = await crudService.GetByIdAsync<CourseClassEntity>(request.CourseClassId, cancellationToken);

                if (courseClass is null)
                {
                    return Result.NotFound("Class not found");
                }

                var hasInstructorAssistantRole = await mediator.Send(new CheckUserRole.Request
                {
                    UserId = request.AssistantInstructorUserId,
                    Role = "instructor-assistant"
                }, cancellationToken);

                if (!hasInstructorAssistantRole.IsSuccess)
                {
                    return Result.Forbidden("User is not an Assistant Instructor.");
                }

                var participation = await crudService.GetAll<ClassParticipationEntity>()
                    .Where(x => x.CourseClass.Id == request.CourseClassId
                        && x.ClassParticipationType == ClassParticipationType.AssistantInstructor)
                    .OrderByDescending(x => x.OperationDate)
                    .FirstOrDefaultAsync(cancellationToken);

                if (participation is not null)
                {
                    if (participation.UserId != request.AssistantInstructorUserId
                            && participation.ClassOperationType == ClassOperationType.Join)
                    {
                        return Result.Conflict("An Assistant Instructor is already assigned to this class.");
                    }
                    if (participation.UserId == request.AssistantInstructorUserId
                            && participation.ClassOperationType == request.ClassOperationType)
                    {
                        return Result.Conflict("The Assistant Instructor is already assigned with the same operation type.");
                    }
                }

                participation = new ClassParticipationEntity()
                {
                    CourseClassId = request.CourseClassId,
                    UserId = request.AssistantInstructorUserId,
                    ClassOperationType = request.ClassOperationType,
                    ClassParticipationType = ClassParticipationType.AssistantInstructor,
                    OperationDate = DateTime.UtcNow
                };

                await crudService.AddAsync(participation, cancellationToken);

                var result = Result.Success(participation.Id);

                return result;
            }
        }
    }
}