namespace Texan.Grade.Application.Operations.AssignmentAttachments.Queries;

public class GetAttachmentByIdQuery
{
    public class Request : IRequest<Result<AssignmentAttachmentEntity>>
    {
        public Guid Id { get; set; }
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Id)
                .NotEmpty().WithMessage("Attachment ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<AssignmentAttachmentEntity>>
    {
        public async Task<Result<AssignmentAttachmentEntity>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Getting attachment: {AttachmentId}", request.Id);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Attachment query failed validation: {AttachmentId}", request.Id);
                return validationError;
            }

            var attachment = await crudService
                .Query<AssignmentAttachmentEntity>()
                .FirstOrDefaultAsync(aa => aa.Id == request.Id, cancellationToken);

            if (attachment is null)
            {
                logger.LogWarning("Attachment not found: {AttachmentId}", request.Id);
                return Result.NotFound("Attachment not found.");
            }

            logger.LogInformation("Attachment retrieved: {AttachmentId}", attachment.Id);
            return Result.Success(attachment);
        }
    }
}