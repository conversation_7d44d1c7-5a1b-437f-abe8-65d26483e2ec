namespace Texan.Grade.Application.Operations.AssignmentAttachments.Queries;

public class GetAttachmentsByAssignmentQuery
{
    public class Request : IRequest<Result<List<AssignmentAttachmentEntity>>>
    {
        public Guid AssignmentId { get; set; }
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.AssignmentId)
                .NotEmpty().WithMessage("Assignment ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<List<AssignmentAttachmentEntity>>>
    {
        public async Task<Result<List<AssignmentAttachmentEntity>>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Getting attachments for assignment: {AssignmentId}", request.AssignmentId);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Attachments query failed validation for assignment: {AssignmentId}", request.AssignmentId);
                return validationError;
            }

            var assignmentExists = await crudService
                .Query<AssignmentEntity>()
                .AnyAsync(a => a.Id == request.AssignmentId, cancellationToken);

            if (!assignmentExists)
            {
                logger.LogWarning("Assignment not found for attachments query: {AssignmentId}", request.AssignmentId);
                return Result.NotFound("Assignment not found.");
            }

            var attachments = await crudService
                .Query<AssignmentAttachmentEntity>()
                .Where(aa => aa.AssignmentId == request.AssignmentId)
                .OrderBy(aa => aa.FileName)
                .ToListAsync(cancellationToken);

            logger.LogInformation("Retrieved {Count} attachments for assignment: {AssignmentId}", attachments.Count, request.AssignmentId);
            return Result.Success(attachments);
        }
    }
}