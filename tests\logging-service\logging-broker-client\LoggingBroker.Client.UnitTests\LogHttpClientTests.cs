﻿using LoggingBroker.Client.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq.Protected;
using Moq;
using System.Net;

namespace LoggingBroker.Client.UnitTests;

public class LogHttpClientTests
{
    [Fact]
    // HttpClient POST isteği doğru URL'e ve method'a gönderiliyor mu?
    public async Task SendBatchAsync_ShouldSend_PostRequest()
    {
        // Arrange
        var handlerMock = new Mock<HttpMessageHandler>();
        handlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(new HttpResponseMessage { StatusCode = HttpStatusCode.OK })
            .Verifiable();

        var httpClient = new HttpClient(handlerMock.Object);

        var options = Options.Create(new LoggingBrokerOptions
        {
            BaseUrl = "http://test",
            LogEndpoint = "/api/v1/logs"
        });
        var loggerMock = new Mock<ILogger<LogHttpClient>>();
        var logHttpClient = new LogHttpClient(httpClient, options, loggerMock.Object);

        var logs = new[] {
                new LogRequestToApi
                {
                    Source = "source",
                    LogLevel = LogLevel.Information,
                    Message = "test",
                    EventUnixTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                }
            };

        // Act
        await logHttpClient.SendBatchAsync(logs, default);

        // Assert
        handlerMock.Protected().Verify(
            "SendAsync",
            Times.Once(),
            ItExpr.Is<HttpRequestMessage>(req =>
                req.Method == HttpMethod.Post &&
                req.RequestUri!.AbsolutePath == "/api/v1/logs"
            ),
            ItExpr.IsAny<CancellationToken>()
        );
    }
    [Fact]
    // HttpClient POST başarısız olursa exception fırlatıyor mu? (Retry policy için en azından 1 kere deneyip hata döner.)
    public async Task SendBatchAsync_ShouldThrow_OnHttpError()
    {
        var handlerMock = new Mock<HttpMessageHandler>();
        handlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(new HttpResponseMessage { StatusCode = HttpStatusCode.InternalServerError });

        var httpClient = new HttpClient(handlerMock.Object);

        var options = Options.Create(new LoggingBrokerOptions
        {
            BaseUrl = "http://test",
            LogEndpoint = "/api/v1/logs",
            MaxRetryCount = 1 // Retry policy için minimumda tut
        });
        var loggerMock = new Mock<ILogger<LogHttpClient>>();
        var logHttpClient = new LogHttpClient(httpClient, options, loggerMock.Object);

        var logs = new[] {
            new LogRequestToApi
            {
                Source = "source",
                LogLevel = LogLevel.Information,
                Message = "test",
                EventUnixTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            }
        };

        // Act-Assert: Exception bekleniyor
        await Assert.ThrowsAnyAsync<Exception>(() => logHttpClient.SendBatchAsync(logs, default));
    }
}