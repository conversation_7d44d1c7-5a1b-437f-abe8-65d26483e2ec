﻿using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Texan.LoggingBroker.Application.Operations.GetLog;
using Texan.LoggingBroker.Application.Services.AsyncQueue;
using Texan.LoggingBroker.Domain.Models;

namespace Texan.LoggingBroker.WebApi.Extensions.Endpoints;

public static class EndpointRouteBuilderExtensions
{
    public static IEndpointRouteBuilder MapRoutes(this IEndpointRouteBuilder endpoints)
    {
        endpoints.MapPost("/api/v1/logs", async (
                                                 [FromBody] List<LogRequest> logRequests,
                                                 IValidator<LogRequest> validator,
                                                 IAsyncQueueService<QueuedLogItem> queue,
                                                 CancellationToken cancellationToken) =>
        {
            foreach (var logRequest in logRequests)
            {
                await validator.ValidateAsync(logRequest, cancellationToken);

                await queue.EnqueueAsync(new QueuedLogItem
                {
                    Request = logRequest,
                }, cancellationToken);
            }

            return Results.Ok("Log received to <PERSON> and enqueued for processing.");
        });

        // QUESTION: Single log post endpoint is required or alternative?

        endpoints.MapGet("/api/v1/logs", async (
                                                [AsParameters] LogQueryParameters queryParams,
                                                IMediator mediator,
                                                CancellationToken cancellationToken) =>
        {
            var logsJson = await mediator.Send(new GetLogsQuery(queryParams), cancellationToken);
            return Results.Ok(logsJson);
        });


        return endpoints;
    }
}
