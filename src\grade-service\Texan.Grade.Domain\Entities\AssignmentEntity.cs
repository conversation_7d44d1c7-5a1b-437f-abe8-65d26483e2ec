namespace Texan.Grade.Domain.Entities;

/// <summary>
/// Represents an assignment in the system
/// </summary>
public class AssignmentEntity : BaseEntity
{
    /// <summary>
    /// Name of the assignment
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Detailed description of the assignment
    /// </summary>
    public string Details { get; set; } = string.Empty;
    
    /// <summary>
    /// Identifier of the course class this assignment belongs to
    /// </summary>
    public string CourseClassId { get; set; } = string.Empty;
    
    /// <summary>
    /// Optional week identifier for the assignment
    /// </summary>
    public string? WeekId { get; set; }
    
    /// <summary>
    /// Order of the assignment within the course
    /// </summary>
    public byte Order { get; set; }
    
    /// <summary>
    /// Date and time when the assignment was assigned to students
    /// </summary>
    public DateTime AssignedAt { get; set; }
    
    /// <summary>
    /// Optional due date for the assignment
    /// </summary>
    public DateTime? DueDate { get; set; }
    
    /// <summary>
    /// Identifier of the instructor who created the assignment
    /// </summary>
    public string CreatedByUserId { get; set; } = string.Empty;
}