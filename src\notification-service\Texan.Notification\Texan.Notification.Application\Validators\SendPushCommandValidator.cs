﻿using FluentValidation;
using Texan.Notification.Application.Features.Commands.PushCommands;

public class SendPushCommandValidator : AbstractValidator<SendPushCommand>
{
    public SendPushCommandValidator()
    {
        RuleFor(x => x.DeviceToken)
            .NotEmpty().WithMessage("DeviceToken is required.")
            .MinimumLength(10).WithMessage("DeviceToken is too short.");

        RuleFor(x => x.Title)
            .NotEmpty().WithMessage("Notification title is required.")
            .MaximumLength(100);

        RuleFor(x => x.Body)
            .NotEmpty().WithMessage("Notification body is required.")
            .MaximumLength(500);
    }
}
