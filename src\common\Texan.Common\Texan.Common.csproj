﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
		<PackageReference Include="Ardalis.Result" Version="10.1.0" />
		<PackageReference Include="FluentValidation" Version="12.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.6" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="9.0.1" />
		<PackageReference Include="System.Text.Json" Version="9.0.6" />
	</ItemGroup>

</Project>
