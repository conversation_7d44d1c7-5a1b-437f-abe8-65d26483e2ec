using Ardalis.Result;
using FluentValidation;
using FluentValidation.Results;
using MockQueryable.Moq;
using Moq;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Application.Operations.Assignments;
using Texan.ClassManagement.Application.Operations.RoleCheck;
using Texan.ClassManagement.Domain.Entities;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Tests.UnitTests.Application.Assignments
{
    public class AssignInstructorToClassTests
    {
        private readonly Mock<ICrudService> _mockCrudService;
        private readonly Mock<IValidator<AssignInstructorToClass.Request>> _mockValidator;
        private readonly Mock<IMediator> _mockMediator;
        private readonly AssignInstructorToClass.Handler _handler;

        public AssignInstructorToClassTests()
        {
            _mockCrudService = new Mock<ICrudService>();
            _mockValidator = new Mock<IValidator<AssignInstructorToClass.Request>>();
            _mockMediator = new Mock<IMediator>();
            _handler = new AssignInstructorToClass.Handler(_mockCrudService.Object, _mockValidator.Object, _mockMediator.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ReturnsSuccess()
        {
            // Arrange
            var request = new AssignInstructorToClass.Request
            {
                CourseClassId = "class1",
                InstructorUserId = "instructor1"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };
            var instructorUser = new UserEntity { Id = "instructor1" };
            var mockParticipationQueryable = new List<ClassParticipationEntity>()
                .AsQueryable()
                .BuildMockDbSet();
            string generatedId = "new-participation-id";

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.InstructorUserId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(instructorUser);

            _mockMediator.Setup(x => x.Send(
                It.IsAny<CheckUserRole.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(Result.Success());

            _mockCrudService.Setup(x => x.GetAll<ClassParticipationEntity>())
                .Returns(mockParticipationQueryable.Object);

            _mockCrudService.Setup(x => x.AddAsync(
                It.IsAny<ClassParticipationEntity>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((ClassParticipationEntity p, CancellationToken _) =>
                    {
                        p.Id = "new-participation-id";
                        return p;
                    });

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(generatedId, result.Value);
        }

        [Fact]
        public async Task Handle_InvalidRequest_ReturnsValidationError()
        {
            // Arrange
            var request = new AssignInstructorToClass.Request
            {
                CourseClassId = "",
                InstructorUserId = ""
            };

            var validationFailures = new List<ValidationFailure>
            {
                new ValidationFailure("CourseClassId", "CourseClassId is required."),
                new ValidationFailure("InstructorUserId", "InstructorUserId is required.")
            };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(2, result.ValidationErrors.Count());
        }

        [Fact]
        public async Task Handle_ClassNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new AssignInstructorToClass.Request
            {
                CourseClassId = "nonexistent",
                InstructorUserId = "instructor1"
            };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((CourseClassEntity)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Class not found", result.Errors.First());
        }

        [Fact]
        public async Task Handle_InstructorNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new AssignInstructorToClass.Request
            {
                CourseClassId = "class1",
                InstructorUserId = "nonexistent"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.InstructorUserId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((UserEntity)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Instructor user not found.", result.Errors.First());
        }

        [Fact]
        public async Task Handle_UserNotInstructor_ReturnsForbidden()
        {
            // Arrange
            var request = new AssignInstructorToClass.Request
            {
                CourseClassId = "class1",
                InstructorUserId = "user1"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };
            var user = new UserEntity { Id = "user1" };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.InstructorUserId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(user);

            _mockMediator.Setup(x => x.Send(
                It.IsAny<CheckUserRole.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(Result.Forbidden());

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("User is not an Instructor", result.Errors.First());
        }

        [Fact]
        public async Task Handle_InstructorAlreadyAssigned_ReturnsConflict()
        {
            // Arrange
            var request = new AssignInstructorToClass.Request
            {
                CourseClassId = "class1",
                InstructorUserId = "instructor1"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };
            var instructorUser = new UserEntity { Id = "instructor1" };
            var fakeList = new List<ClassParticipationEntity>
            {
                new ClassParticipationEntity
                {
                    CourseClassId = "class1",
                    UserId = "existingInstructor",
                    ClassParticipationType = ClassParticipationType.MainInstructor,
                    CourseClass = new CourseClassEntity { Id = "class1" }
                }
            };

            var mockParticipationQueryable = fakeList.AsQueryable().BuildMockDbSet();

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.InstructorUserId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(instructorUser);

            _mockMediator.Setup(x => x.Send(
                It.IsAny<CheckUserRole.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(Result.Success());

            _mockCrudService.Setup(x => x.GetAll<ClassParticipationEntity>())
                .Returns(mockParticipationQueryable.Object);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("An Instructor is already assigned to this class.", result.Errors.First());

        }
    }
}