services:
  notification-service:
    build:
      context: .
      dockerfile: Texan.Notification/Texan.Notification.Presentation.WebApi/Dockerfile
    container_name: Texan.Notification
    ports:
      - "5005:8080"
    environment:
      - ASPNETCORE_URLS=http://+:8080
      - ASPNETCORE_ENVIRONMENT=Development

      # MailSettings
      - MailSettings__SmtpServer=smtp.gmail.com
      - MailSettings__Port=587
      - MailSettings__SenderName=Texan Notifier
      - MailSettings__SenderEmail=<EMAIL>
      - MailSettings__Username=<EMAIL>
      - MailSettings__Password=ztbympyzdfntgjxb

      # TwilioSettings
      - Twilio__AccountSid=ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
      - Twilio__AuthToken=your_auth_token
      - Twilio__FromPhone=+**********
      - Twilio__UseDummy=true

      # Push Notification (Firebase Admin SDK)
      - FIREBASE_SERVICE_ACCOUNT_JSON={"type":"service_account","project_id":"...","private_key_id":"...","private_key":"-----BEGIN PRIVATE KEY-----\\n...\\n-----END PRIVATE KEY-----\\n", ...}

      # Jwt Settings
      - Jwt__Issuer=TexanIssuer
      - Jwt__Audience=TexanAudience
      - Jwt__Key=M8v@!nZ5$kP2^aQxL0#dRfE7sJgU1hW3cYzTbN9uVrXKmCpO

    networks:
      - texan-network

networks:
  texan-network:
    external: true
