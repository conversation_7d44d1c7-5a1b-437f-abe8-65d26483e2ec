namespace Texan.Grade.Application.Operations.Assignment.Commands;

public class UpdateAssignmentCommand
{
    public class Request : IRequest<Result<AssignmentEntity>>
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? Details { get; set; }
        public string? CourseClassId { get; set; }
        public string? WeekId { get; set; }
        public byte? Order { get; set; }
        public DateTime? AssignedAt { get; set; }
        public DateTime? DueDate { get; set; }
        public string? CreatedByUserId { get; set; }
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Id)
                .NotEmpty().WithMessage("Assignment ID is required.");
            
            RuleFor(r => r.Name)
                .MaximumLength(200).WithMessage("Assignment name cannot exceed 200 characters.")
                .When(r => !string.IsNullOrEmpty(r.Name));
            
            RuleFor(r => r.Details)
                .MaximumLength(2000).WithMessage("Assignment details cannot exceed 2000 characters.")
                .When(r => !string.IsNullOrEmpty(r.Details));
            
            RuleFor(r => r.CourseClassId)
                .NotEmpty().WithMessage("Course Class ID cannot be empty.")
                .When(r => r.CourseClassId is not null);
            
            RuleFor(r => r.CreatedByUserId)
                .NotEmpty().WithMessage("Instructor ID cannot be empty.")
                .When(r => r.CreatedByUserId is not null);
            
            RuleFor(r => r.AssignedAt)
                .NotEmpty().WithMessage("Assignment date cannot be empty.")
                .When(r => r.AssignedAt.HasValue);
            
            RuleFor(r => r.DueDate)
                .GreaterThan(r => r.AssignedAt)
                .When(r => r.DueDate.HasValue && r.AssignedAt.HasValue)
                .WithMessage("Due date must be after assignment date.");
        }
    }

   public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<AssignmentEntity>>
    {
        public async Task<Result<AssignmentEntity>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Updating assignment: {AssignmentId}", request.Id);
            
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Assignment update failed validation: {AssignmentId}", request.Id);
                return validationError;
            }

            var assignment = await crudService
                .Query<AssignmentEntity>()
                .FirstOrDefaultAsync(a => a.Id == request.Id, cancellationToken);

            if (assignment is null)
            {
                logger.LogWarning("Assignment not found for update: {AssignmentId}", request.Id);
                return Result.NotFound("Assignment not found.");
            }

            if (!string.IsNullOrEmpty(request.Name) && request.Name != assignment.Name)
            {
                var courseClassIdToCheck = request.CourseClassId ?? assignment.CourseClassId;
                
                var existingAssignment = await crudService
                    .Query<AssignmentEntity>()
                    .FirstOrDefaultAsync(a => a.Name == request.Name && 
                                        a.CourseClassId == courseClassIdToCheck && 
                                        a.Id != request.Id, cancellationToken);

                if (existingAssignment is not null)
                {
                    logger.LogWarning("Assignment update failed - duplicate name: {Name} in course {CourseClassId}", request.Name, courseClassIdToCheck);
                    var error = new ValidationError
                    {
                        Identifier = nameof(request.Name),
                        ErrorMessage = "An assignment with this name already exists in the course class."
                    };
                    return Result.Invalid(error);
                }
            }

            var assignedAtToCheck = request.AssignedAt ?? assignment.AssignedAt;
            var dueDateToCheck = request.DueDate ?? assignment.DueDate;
            
            if (dueDateToCheck.HasValue && dueDateToCheck.Value <= assignedAtToCheck)
            {
                logger.LogWarning("Assignment update failed - invalid dates: {AssignmentId}", request.Id);
                var error = new ValidationError
                {
                    Identifier = request.DueDate.HasValue ? nameof(request.DueDate) : nameof(request.AssignedAt),
                    ErrorMessage = "Due date must be after assignment date."
                };
                return Result.Invalid(error);
            }

            if (!string.IsNullOrEmpty(request.Name))
                assignment.Name = request.Name;
            if (!string.IsNullOrEmpty(request.Details))
                assignment.Details = request.Details;
            if (!string.IsNullOrEmpty(request.CourseClassId))
                assignment.CourseClassId = request.CourseClassId;
            if (request.WeekId is not null) 
                assignment.WeekId = request.WeekId;
            if (request.Order.HasValue)
                assignment.Order = request.Order.Value;
            if (request.AssignedAt.HasValue)
                assignment.AssignedAt = request.AssignedAt.Value;
            if (request.DueDate is not null)
                assignment.DueDate = request.DueDate;
            if (!string.IsNullOrEmpty(request.CreatedByUserId))
                assignment.CreatedByUserId = request.CreatedByUserId;

            await crudService.UpdateAsync(assignment, cancellationToken);
            
            logger.LogInformation("Assignment updated: {AssignmentId}", assignment.Id);
            return Result.Success(assignment);
        }
    }
}