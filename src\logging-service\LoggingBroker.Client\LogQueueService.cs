﻿using LoggingBroker.Client.Models;
using System.Threading.Channels;

namespace LoggingBroker.Client;

public class LogQueueService
{
    private readonly Channel<LogRequestToApi> _channel;

    public LogQueueService(int capacity)
    {
        var options = new BoundedChannelOptions(capacity)
        {
            FullMode = BoundedChannelFullMode.Wait
        };
        _channel = Channel.CreateBounded<LogRequestToApi>(options);
    }

    public bool TryEnqueue(LogRequestToApi log)
    {
        return _channel.Writer.TryWrite(log);
    }

    public IAsyncEnumerable<LogRequestToApi> ReadAllAsync(CancellationToken cancellationToken)
    {
        return _channel.Reader.ReadAllAsync(cancellationToken);
    }

    public void Complete() => _channel.Writer.Complete();
}