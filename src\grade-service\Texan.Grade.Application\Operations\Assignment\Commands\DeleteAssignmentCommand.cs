namespace Texan.Grade.Application.Operations.Assignment.Commands;

public class DeleteAssignmentCommand
{
    public class Request : IRequest<Result<bool>>
    {
        public Guid Id { get; set; }
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Id)
                .NotEmpty().WithMessage("Assignment ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<bool>>
    {
        public async Task<Result<bool>> <PERSON>le(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Deleting assignment: {AssignmentId}", request.Id);
            
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Assignment deletion failed validation: {AssignmentId}", request.Id);
                return validationError;
            }

            var assignment = await crudService
                .Query<AssignmentEntity>()
                .FirstOrDefaultAsync(a => a.Id == request.Id, cancellationToken);

            if (assignment is null)
            {
                logger.LogWarning("Assignment not found for deletion: {AssignmentId}", request.Id);
                return Result.NotFound("Assignment not found.");
            }
            
            var hasGrades = await crudService
                .Query<GradeEntity>()
                .AnyAsync(g => g.AssignmentId == request.Id, cancellationToken);

            if (hasGrades)
            {
                logger.LogWarning("Assignment deletion blocked - has grades: {AssignmentId}", request.Id);
                var error = new ValidationError
                {
                    Identifier = nameof(request.Id),
                    ErrorMessage = "Cannot delete assignment that has grades associated with it."
                };
                return Result.Invalid(error);
            }
            
            var hasAttachments = await crudService
                .Query<AssignmentAttachmentEntity>()
                .AnyAsync(aa => aa.AssignmentId == request.Id, cancellationToken);

            if (hasAttachments)
            {
                logger.LogWarning("Assignment deletion blocked - has attachments: {AssignmentId}", request.Id);
                var error = new ValidationError
                {
                    Identifier = nameof(request.Id),
                    ErrorMessage = "Cannot delete assignment that has attachments. Please remove attachments first."
                };
                return Result.Invalid(error);
            }

            await crudService.DeleteAsync(assignment, cancellationToken);
            
            logger.LogInformation("Assignment deleted: {AssignmentId}", request.Id);
            return Result.Success(true);
        }
    }
}