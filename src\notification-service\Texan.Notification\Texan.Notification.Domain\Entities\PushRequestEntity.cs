﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Texan.Notification.Domain.Entities
{
    public class PushRequestEntity
    {
        public string DeviceToken { get; set; } = default!;
        public string Title { get; set; } = default!;
        public string Body { get; set; } = default!;
        public DateTime SentAt { get; set; } = DateTime.UtcNow;
        public bool IsSuccessful { get; set; }
    }
}
