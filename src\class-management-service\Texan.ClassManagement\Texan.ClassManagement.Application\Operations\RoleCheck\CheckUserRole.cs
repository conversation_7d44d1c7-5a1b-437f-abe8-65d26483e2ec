﻿using Ardalis.Result;
using FluentValidation;
using SMediator.Core.Abstractions;

namespace Texan.ClassManagement.Application.Operations.RoleCheck
{
    public class CheckUserRole
    {
        public class Request : IRequest<Result>
        {
            public string UserId { get; set; } = null!;
            public string Role { get; set; } = null!;
        }

        public class RequestValidator : AbstractValidator<Request>
        {
            public RequestValidator()
            {
                RuleFor(r => r.UserId)
                    .NotEmpty().WithMessage("UserId is required.");
                RuleFor(r => r.Role)
                    .NotEmpty().WithMessage("Role is required.");
            }
        }

        public class Handler(IValidator<Request> validator) : IRequestHandler<Request, Result>
        {
            public async Task<Result> Handle(Request request, CancellationToken cancellationToken)
            {
                var validationResult = await validator.ValidateAsync(request, cancellationToken);
                if (!validationResult.IsValid)
                {
                    var validationErrors = validationResult.Errors
                        .Select(x => new ValidationError
                        {
                            Identifier = x.PropertyName,
                            ErrorMessage = x.ErrorMessage
                        });
                    return Result.Invalid(validationErrors);
                }

                // TODO: ask auth api if user is in the role

                bool hasRole = true; // Replace with actual role check logic

                return hasRole ? Result.Success() : Result.Forbidden();
            }
        }
    }
}