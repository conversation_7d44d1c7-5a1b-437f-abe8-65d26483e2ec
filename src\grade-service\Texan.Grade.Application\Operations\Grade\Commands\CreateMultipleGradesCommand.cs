namespace Texan.Grade.Application.Operations.Grade.Commands;

public class CreateMultipleGradesCommand
{
    public class Request : IRequest<Result<BulkCreateResult>>
    {
        public List<GradeCreateRequest> Grades { get; set; } = new();
    }

    public class GradeCreateRequest
    {
        public string StudentId { get; set; } = string.Empty;
        public Guid AssignmentId { get; set; }
        public string GivenByUserId { get; set; } = string.Empty;
        public decimal Grade { get; set; }
        public bool IsFinal { get; set; }
    }

    public class BulkCreateResult
    {
        public int TotalSubmitted { get; set; }
        public int Successful { get; set; }
        public int Failed { get; set; }
        public List<GradeResult> Results { get; set; } = new();
    }

    public class GradeResult
    {
        public int Index { get; set; }
        public bool Success { get; set; }
        public string? GradeId { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Grades)
                .NotEmpty().WithMessage("At least one grade is required.")
                .Must(grades => grades.Count <= 100).WithMessage("Cannot create more than 100 grades at once.");

            RuleForEach(r => r.Grades).SetValidator(new GradeCreateRequestValidator());
        }
    }

    public class GradeCreateRequestValidator : AbstractValidator<GradeCreateRequest>
    {
        public GradeCreateRequestValidator()
        {
            RuleFor(r => r.StudentId)
                .NotEmpty().WithMessage("Student ID is required.");
            
            RuleFor(r => r.AssignmentId)
                .NotEmpty().WithMessage("Assignment ID is required.");
            
            RuleFor(r => r.GivenByUserId)
                .NotEmpty().WithMessage("Instructor ID is required.");
            
            RuleFor(r => r.Grade)
                .GreaterThanOrEqualTo(0).WithMessage("Grade must be greater than or equal to 0.")
                .LessThanOrEqualTo(100).WithMessage("Grade must be less than or equal to 100.");
        }
    }

   public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<BulkCreateResult>>
    {
        public async Task<Result<BulkCreateResult>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Creating {Count} grades", request.Grades.Count);
            
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Bulk grade creation failed validation");
                return validationError;
            }

            var result = new BulkCreateResult
            {
                TotalSubmitted = request.Grades.Count
            };

            var businessRuleErrors = await ValidateBusinessRules(request.Grades, cancellationToken);
            if (businessRuleErrors.Any())
            {
                logger.LogWarning("Bulk grade creation failed - {Count} business rule violations", businessRuleErrors.Count);
                foreach (var (index, error) in businessRuleErrors)
                {
                    result.Results.Add(new GradeResult
                    {
                        Index = index,
                        Success = false,
                        Errors = new List<string> { error.ErrorMessage }
                    });
                }
                result.Failed = businessRuleErrors.Count;
                result.Successful = 0;
                return Result.Success(result);
            }

            var gradesToCreate = new List<GradeEntity>();
            for (int i = 0; i < request.Grades.Count; i++)
            {
                var gradeRequest = request.Grades[i];
                var gradeEntity = new GradeEntity
                {
                    Id = Guid.NewGuid(),
                    StudentId = gradeRequest.StudentId,
                    AssignmentId = gradeRequest.AssignmentId,
                    GivenByUserId = gradeRequest.GivenByUserId,
                    Grade = gradeRequest.Grade,
                    IsFinal = gradeRequest.IsFinal
                };

                gradesToCreate.Add(gradeEntity);
                result.Results.Add(new GradeResult
                {
                    Index = i,
                    Success = true,
                    GradeId = gradeEntity.Id.ToString()
                });
            }

            try
            {
                await crudService.AddMultipleAsync(gradesToCreate.ToArray(), cancellationToken);
                result.Successful = gradesToCreate.Count;
                result.Failed = 0;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Bulk grade creation failed in database");
                foreach (var resultItem in result.Results)
                {
                    resultItem.Success = false;
                    resultItem.GradeId = null;
                    resultItem.Errors.Add("Database operation failed.");
                }
                result.Successful = 0;
                result.Failed = request.Grades.Count;
            }

            logger.LogInformation("Bulk grade creation completed: {Successful} successful, {Failed} failed", result.Successful, result.Failed);
            return Result.Success(result);
        }

        private async Task<List<(int Index, ValidationError Error)>> ValidateBusinessRules(
            List<GradeCreateRequest> grades, 
            CancellationToken cancellationToken)
        {
            var errors = new List<(int Index, ValidationError Error)>();

            var groupedByStudentAssignment = grades
                .Select((grade, index) => new { grade, index })
                .GroupBy(x => new { x.grade.StudentId, x.grade.AssignmentId });

            foreach (var group in groupedByStudentAssignment)
            {
                if (group.Count() > 1)
                {
                    foreach (var item in group.Skip(1)) 
                    {
                        var error = new ValidationError
                        {
                            Identifier = $"Grades[{item.index}]",
                            ErrorMessage = $"Duplicate grade for student '{group.Key.StudentId}' and assignment '{group.Key.AssignmentId}' within the batch."
                        };
                        errors.Add((item.index, error));
                    }
                }
            }
            
            var uniqueGrades = grades
                .Select((grade, index) => new { grade, index })
                .GroupBy(x => new { x.grade.StudentId, x.grade.AssignmentId })
                .Select(g => g.First())
                .ToList();

            foreach (var item in uniqueGrades)
            {
                var assignment = await crudService
                    .Query<AssignmentEntity>()
                    .FirstOrDefaultAsync(a => a.Id == item.grade.AssignmentId, cancellationToken);

                if (assignment == null)
                {
                    var error = new ValidationError
                    {
                        Identifier = $"Grades[{item.index}].AssignmentId",
                        ErrorMessage = $"Assignment with ID '{item.grade.AssignmentId}' does not exist."
                    };
                    errors.Add((item.index, error));
                    continue;
                }
                
                var existingGrade = await crudService
                    .Query<GradeEntity>()
                    .FirstOrDefaultAsync(g => g.StudentId == item.grade.StudentId && g.AssignmentId == item.grade.AssignmentId, cancellationToken);

                if (existingGrade != null)
                {
                    var error = new ValidationError
                    {
                        Identifier = $"Grades[{item.index}]",
                        ErrorMessage = $"A grade already exists for student '{item.grade.StudentId}' and assignment '{item.grade.AssignmentId}'."
                    };
                    errors.Add((item.index, error));
                }
            }

            return errors;
        }
    }
}