﻿using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Domain.Entities
{
    public class ClassParticipationEntity : BaseEntity
    {
        public DateTime OperationDate { get; set; }
        public ClassParticipationType ClassParticipationType { get; set; }
        public ClassOperationType ClassOperationType { get; set; }

        // Foreign Keys
        public string UserId { get; set; } = null!;

        public string CourseClassId { get; set; } = null!;

        // Navigation

        public CourseClassEntity CourseClass { get; set; } = null!;
    }
}