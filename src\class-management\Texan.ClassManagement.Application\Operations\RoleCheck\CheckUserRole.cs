﻿using Ardalis.Result;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Application.Operations.RoleCheck
{
    public class CheckUserRole
    {
        public class Request : IRequest<Result>
        {
            public string UserId { get; set; } = null!;
            public string Role { get; set; } = null!;
        }

        public class RequestValidator : AbstractValidator<Request>
        {
            public RequestValidator()
            {
                RuleFor(r => r.UserId)
                    .NotEmpty().WithMessage("UserId is required.");
                RuleFor(r => r.Role)
                    .NotEmpty().WithMessage("Role is required.");
            }
        }

        public class Handler(ICrudService crudService, IValidator<Request> validator) : IRequestHandler<Request, Result>
        {
            public async Task<Result> Handle(Request request, CancellationToken cancellationToken)
            {
                var validationResult = await validator.ValidateAsync(request, cancellationToken);
                if (!validationResult.IsValid)
                {
                    var validationErrors = validationResult.Errors
                        .Select(x => new ValidationError
                        {
                            Identifier = x.PropertyName,
                            ErrorMessage = x.ErrorMessage
                        });
                    return Result.Invalid(validationErrors);
                }
                var hasRole = await crudService.GetAll<UserEntity>()
                    .Where(u => u.Id == request.UserId
                        && u.Roles.Any(r => r.Name == request.Role))
                    .AnyAsync();

                return hasRole ? Result.Success() : Result.Forbidden();
            }
        }
    }
}