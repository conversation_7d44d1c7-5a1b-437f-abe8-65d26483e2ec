# LoggingBroker Client Konfigürasyon Rehberi

Bu doküman, **common.env** ve **docker-compose.yaml** kullanarak LoggingBroker Client konfigürasyonunu nasıl tekilleştireceğinizi gösterir.

## 1. Ortak `.env` Dosyası (`LoggingBroker.Client.Common.env`)
Base URL ve endpoint tüm servisler için sabit olduğundan tek bir dosyada tutulur.
Bu dosya LoggingBroker.Client kütüphanesi içerisinde mevcutur:

```dotenv
# LoggingBroker.Client.Common.env
LoggingBroker__BaseUrl=http://logging-broker:8080
LoggingBroker__LogEndpoint=/api/v1/logs
```

Servislerinizin her birinde bu dosyayı referans edin:

```yaml
services:
  service-A:
    # …
    env_file:
      - ../LoggingBroker.Client.Common.env
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      <<: *loggingbroker-overrides
```

---

## 2. <PERSON><PERSON> – YAML Anchor
Tüm servislerde ortak olanlardan farklı parametreleri aşağıdaki anchor ile yönetin:

```yaml
version: '3.8'

# 1) Servise özel değişen ayarlar:
  LoggingBroker__BatchSize: "100"                         
  LoggingBroker__BatchTimeLimitSeconds: "5"               
  LoggingBroker__SendIntervalSeconds: "3"                 
  LoggingBroker__MaxQueueSize: "10000"                    
  LoggingBroker__CircuitBreakerFailureRate: "0.5"         
  LoggingBroker__CircuitBreakerSamplingSeconds: "30"      
  LoggingBroker__CircuitBreakerMinimumThroughput: "10"    
  LoggingBroker__CircuitBreakerBreakDurationSeconds: "30" 
  LoggingBroker__RetryBackoffBaseSeconds: "2"             
  LoggingBroker__MaxRetryCount: "5"                       
  LoggingBroker__HttpClientTimeoutSeconds: "30"           
  LoggingBroker__MinimumLevel: "Information"

services:
  service-A:
    build: ./service-A
    env_file:
      - ../LoggingBroker.Client.Common.env
    environment:
      # Sadece bu serviste batch boyutu farklı olsun
      LoggingBroker__BatchSize: "50"

  service-B:
    build: ./service-B
    env_file:
      - ../LoggingBroker.Client.Common.env
    environment:
      # Sadece bu serviste retry sayısı farklı olsun
      LoggingBroker__MaxRetryCount: "10"

  service-C:
    build: ./service-C
    env_file:
      - ../LoggingBroker.Client.Common.env
      # Hiçbir override gerekmiyor, default kullanılır.

```

---

## 3. Servis Kodunda Tek Satır Entegrasyon

`Program.cs` dosyanıza aşağıdaki satırı ekleyin; gerisini kütüphane içindeki extension metodu halleder:

```csharp
var builder = WebApplication.CreateBuilder(args);

// 1 satırla LoggingBroker Client tüm DI ve konfigürasyonu otomatik kaydedin:
builder.Logging.AddLoggingBroker();

var app = builder.Build();
// … middleware, endpoint’ler vs.
app.Run();
```