<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>49d0f971-3e62-426f-b439-d638fc479e04</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..\..</DockerfileContext>
    <DockerComposeProjectPath>..\..\..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.Result.AspNetCore" Version="10.1.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1-Preview.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Texan.AuthBroker.Application\Texan.AuthBroker.Application.csproj" />
    <ProjectReference Include="..\Texan.AuthBroker.Infrastructure\Texan.AuthBroker.Infrastructure.csproj" />
  </ItemGroup>

</Project>
