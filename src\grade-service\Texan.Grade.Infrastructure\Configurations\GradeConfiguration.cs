using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Texan.Grade.Domain.Entities;

namespace Texan.Grade.Infrastructure.Configurations;

/// <summary>
/// Entity Framework configuration for GradeEntity
/// </summary>
public class GradeConfiguration : IEntityTypeConfiguration<GradeEntity>
{
    public void Configure(EntityTypeBuilder<GradeEntity> builder)
    {
        // Table name
        builder.ToTable("Grades");

        // Primary key
        builder.HasKey(g => g.Id);

        // Properties
        builder.Property(g => g.Id)
            .IsRequired();

        builder.Property(g => g.StudentId)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(g => g.AssignmentId)
            .IsRequired();

        builder.Property(g => g.GivenByUserId)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(g => g.Grade)
            .IsRequired()
            .HasPrecision(5, 2); // Precision for decimal grades, e.g., 99.99     

        builder.Property(g => g.IsFinal)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(g => g.CreatedAt)
            .IsRequired();

        builder.Property(g => g.UpdatedAt)
            .IsRequired(false);

        // Indexes for common queries
        builder.HasIndex(g => g.StudentId)
            .HasDatabaseName("IX_Grades_StudentId");

        builder.HasIndex(g => g.AssignmentId)
            .HasDatabaseName("IX_Grades_AssignmentId");

        builder.HasIndex(g => g.GivenByUserId)
            .HasDatabaseName("IX_Grades_GivenByUserId");

        builder.HasIndex(g => new { g.StudentId, g.AssignmentId })
            .IsUnique()
            .HasDatabaseName("IX_Grades_Student_Assignment_Unique");

        builder.HasIndex(g => g.IsFinal)
            .HasDatabaseName("IX_Grades_IsFinal");
    }
}