namespace Texan.Grade.Domain.Entities;

/// <summary>
/// Represents a grade given to a student for an assignment
/// </summary>
public class GradeEntity : BaseEntity
{
    /// <summary>
    /// Identifier of the student who received the grade
    /// </summary>
    public string StudentId { get; set; } = string.Empty;
    
    /// <summary>
    /// Identifier of the assignment this grade is for
    /// </summary>
    public Guid AssignmentId { get; set; }
    
    /// <summary>
    /// Identifier of the instructor who gave the grade
    /// </summary>
    public string GivenByUserId { get; set; } = string.Empty;
    
    /// <summary>
    /// The grade value
    /// </summary>
    public decimal Grade { get; set; }
    
    /// <summary>
    /// Indicates if the grade is final and cannot be updated
    /// </summary>
    public bool IsFinal { get; set; }
}