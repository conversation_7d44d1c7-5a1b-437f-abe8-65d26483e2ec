{"ReverseProxy": {"Routes": {"auth": {"ClusterId": "authCluster", "Match": {"Path": "/api/v1/auth/{**catch-all}"}}, "logging": {"ClusterId": "logCluster", "Match": {"Path": "/api/v1/logs/{**catch-all}"}}, "class-management": {"ClusterId": "classManagementCluster", "Match": {"Path": "/api/v1/class-management/{**catch-all}"}}}, "Clusters": {"authCluster": {"Destinations": {"authService": {"Address": "http://authbroker:8080/"}}}, "logCluster": {"Destinations": {"logService": {"Address": "http://logging-broker:8080/"}}}, "classManagementCluster": {"Destinations": {"classService": {"Address": "http://class-management:8080/"}}}}}}