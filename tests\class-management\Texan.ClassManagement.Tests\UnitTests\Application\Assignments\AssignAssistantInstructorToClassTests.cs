﻿using Ardalis.Result;
using FluentValidation;
using FluentValidation.Results;
using MockQueryable.Moq;
using Moq;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Application.Operations.Assignments;
using Texan.ClassManagement.Application.Operations.RoleCheck;
using Texan.ClassManagement.Domain.Entities;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Tests.UnitTests.Application.Assignments
{
    public class AssignAssistantInstructorToClassTests
    {
        private readonly Mock<ICrudService> _mockCrudService;
        private readonly Mock<IValidator<AssignAssistantInstructorToClass.Request>> _mockValidator;
        private readonly Mock<IMediator> _mockMediator;
        private readonly AssignAssistantInstructorToClass.Handler _handler;

        public AssignAssistantInstructorToClassTests()
        {
            _mockCrudService = new Mock<ICrudService>();
            _mockValidator = new Mock<IValidator<AssignAssistantInstructorToClass.Request>>();
            _mockMediator = new Mock<IMediator>();
            _handler = new AssignAssistantInstructorToClass.Handler(_mockCrudService.Object, _mockValidator.Object, _mockMediator.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ReturnsSuccess()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "class1",
                AssistantInstructorUserId = "assistantInstructor1"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };
            var assistantInstructorUser = new UserEntity { Id = "assistantInstructor1" };
            var mockParticipationQueryable = new List<ClassParticipationEntity>()
                .AsQueryable()
                .BuildMockDbSet();
            string generatedId = "new-participation-id";

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.AssistantInstructorUserId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(assistantInstructorUser);

            _mockMediator.Setup(x => x.Send(
                It.IsAny<CheckUserRole.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(Result.Success());

            _mockCrudService.Setup(x => x.GetAll<ClassParticipationEntity>())
                .Returns(mockParticipationQueryable.Object);

            _mockCrudService.Setup(x => x.AddAsync(
                It.IsAny<ClassParticipationEntity>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((ClassParticipationEntity p, CancellationToken _) =>
                    {
                        p.Id = "new-participation-id";
                        return p;
                    });

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(generatedId, result.Value);
        }

        [Fact]
        public async Task Handle_InvalidRequest_ReturnsValidationErrors()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "",
                AssistantInstructorUserId = ""
            };

            var validationFailures = new List<ValidationFailure>
            {
                new ValidationFailure("CourseClassId", "CourseClassId is required."),
                new ValidationFailure("AssistantInstructorUserId", "AssistantInstructorUserId is required.")
            };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(2, result.ValidationErrors.Count());
        }

        [Fact]
        public async Task Handle_ClassNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "nonexistent",
                AssistantInstructorUserId = "assistantInstructor1"
            };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((CourseClassEntity)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Class not found", result.Errors.First());
        }

        [Fact]
        public async Task Handle_AssistantInstructorNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "class1",
                AssistantInstructorUserId = "nonexistent"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.AssistantInstructorUserId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((UserEntity)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Assistant Instructor user not found.", result.Errors.First());
        }

        [Fact]
        public async Task Handle_UserNotAssistantInstructor_ReturnsForbidden()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "class1",
                AssistantInstructorUserId = "assistantInstructor1"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };
            var assistantInstructorUser = new UserEntity { Id = "assistantInstructor1" };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.AssistantInstructorUserId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(assistantInstructorUser);

            _mockMediator.Setup(x => x.Send(
                It.IsAny<CheckUserRole.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(Result.Forbidden("User is not an Assistant Instructor."));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("User is not an Assistant Instructor.", result.Errors.First());
        }

        [Fact]
        public async Task Handle_AssistantInstructorAlreadyAssigned_ReturnsConflict()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "class1",
                AssistantInstructorUserId = "assistantInstructor1"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };
            var assistantInstructorUser = new UserEntity { Id = "assistantInstructor1" };
            var fakeList = new List<ClassParticipationEntity>()
            {
                new ClassParticipationEntity
                {
                    CourseClassId = "class1",
                    UserId = "existingAssistantInstructor",
                    ClassParticipationType = ClassParticipationType.AssistantInstructor,
                    CourseClass = new CourseClassEntity { Id = "class1" }
                }
            };

            var mockParticipationQueryable = fakeList.AsQueryable().BuildMockDbSet();

            _mockValidator.Setup(x => x.ValidateAsync(request, default))
                .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.AssistantInstructorUserId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(assistantInstructorUser);

            _mockMediator.Setup(x => x.Send(
                It.IsAny<CheckUserRole.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(Result.Success());

            _mockCrudService.Setup(x => x.GetAll<ClassParticipationEntity>())
                .Returns(mockParticipationQueryable.Object);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("An Assistant Instructor is already assigned to this class.", result.Errors.First());
        }
    }
}
