﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Text;

namespace Texan.Common.LoggingMiddlewares;

internal sealed class RequestLoggingMiddleware(RequestDelegate next, ILogger<RequestLoggingMiddleware> logger)
{
    public async Task InvokeAsync(HttpContext context)
    {
        context.Request.EnableBuffering();
        var request = context.Request;

        logger.LogInformation("Request started. Trace Id: {RequestTraceId}, Method: {Method}, Path: {Path}, QueryString: {QueryString}, Request Content Length: {ReqContentLength}, Remote Ip: {RemoteIp}",
            context.TraceIdentifier,
            request.Method,
            request.Path,
            request.QueryString,
            context.Request.ContentLength ?? -1,
            context.Connection.RemoteIpAddress?.ToString());

        await next(context);

        if (context.Response.StatusCode >= 400)
        {
            var requestBodyContent = await ReadRequestBodyAsync(context.Request);

            logger.LogWarning("Request unsuccessful. Status Code: {Status}, Trace Id: {RequestTraceId}, Method: {Method}, Path: {Path}, QueryString: {QueryString}, Remote Ip: {RemoteIp}, Response Content Length: {RespContentLength}, Payload: \"{Payload}\"",
                context.Response.StatusCode,
                context.TraceIdentifier,
                request.Method,
                request.Path,
                request.QueryString,
                context.Connection.RemoteIpAddress?.ToString(),
                context.Response.ContentLength ?? -1,
                requestBodyContent);

            return;
        }

        logger.LogInformation("Request successful. Trace Id: {RequestTraceId}, Response: {StatusCode}", context.TraceIdentifier, context.Response.StatusCode);
    }

    private static async Task<string> ReadRequestBodyAsync(HttpRequest request)
    {
        if (request.Body is null || !request.Body.CanRead || !request.Body.CanSeek)
        {
            return "body-not-read";
        }
        request.Body.Seek(0, SeekOrigin.Begin); // Ensure the stream is at the beginning

        using var reader = new StreamReader(request.Body, encoding: Encoding.UTF8,
            detectEncodingFromByteOrderMarks: false,
            leaveOpen: true);
        var body = await reader.ReadToEndAsync();
        request.Body.Seek(0, SeekOrigin.Begin); // Reset the stream position for further processing
        if (string.IsNullOrWhiteSpace(body))
        {
            return "empty-body";
        }

        return body;
    }
}