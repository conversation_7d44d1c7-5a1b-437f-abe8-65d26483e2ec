﻿using Texan.Gateway.Api.Services.Abstract;
using Texan.Gateway.Api.Services.Concrete;

namespace Texan.Gateway.Api.Extensions
{
    public static class ApiExtensions
    {
        public static void AddApiExtensions(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen();
            services.AddReverseProxy()
                .LoadFromConfig(configuration.GetSection("ReverseProxy"));

            services.AddCors(options =>
            {
                options.AddPolicy("AllowAllOrigins",
                    builder =>
                    {
                        builder.AllowAnyOrigin()
                               .AllowAnyMethod()
                               .AllowAnyHeader();
                    });
            });
            services.AddScoped<IHealthCheckApiService, HealthCheckApiService>();
            services.AddHttpClient();
        }

        public static IConfigurationBuilder AddCustomJsonConfigurations(this IConfigurationBuilder builder)
        {
            return builder
                .AddJsonFile("AppConfiguration/reverse-proxy.json", optional: false, reloadOnChange: true)
                .AddJsonFile("AppConfiguration/healthcheck-config.json", optional: false, reloadOnChange: true);
        }
    }
}