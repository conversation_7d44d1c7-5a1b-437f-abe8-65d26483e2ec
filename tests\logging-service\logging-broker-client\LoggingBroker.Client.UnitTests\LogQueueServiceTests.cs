﻿using FluentAssertions;
using LoggingBroker.Client.Models;
using Microsoft.Extensions.Logging;

namespace LoggingBroker.Client.UnitTests;

public class LogQueueServiceTests
{
    [Fact]
    // Kuyruk dolu değilse TryEnqueue true dönmeli, hata atmamalı.
    public void TryEnqueue_ShouldAddItem_WhenQueueIsNotFull()
    {
        // Arrange
        var queue = new LogQueueService(10);
        var log = new LogRequestToApi
        {
            Source = "test",
            LogLevel = LogLevel.Information,
            Message = "message",
            EventUnixTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };

        // Act
        var result = queue.TryEnqueue(log);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    // ReadAllAsync çağrıldığında kuyruktaki tüm loglar okunmalı.
    public async Task ReadAllAsync_ShouldReturnEnqueuedItem()
    {
        var queue = new LogQueueService(10);
        var log = new LogRequestToApi
        {
            Source = "test",
            LogLevel = LogLevel.Warning,
            Message = "msg",
            EventUnixTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };

        queue.TryEnqueue(log);

        var cts = new CancellationTokenSource(200);
        var items = new List<LogRequestToApi>();
        await foreach (var item in queue.ReadAllAsync(cts.Token))
        {
            items.Add(item);
            break;
        }
        items.Should().ContainSingle().Which.Should().BeEquivalentTo(log);
    }

    [Fact]
    // Kuyruk dolunca TryEnqueue false dönmeli, hiçbir hata atmamalı.
    public void TryEnqueue_ShouldReturnFalse_WhenQueueIsFull()
    {
        var queue = new LogQueueService(2);
        queue.TryEnqueue(new LogRequestToApi { Source = "a", LogLevel = LogLevel.Information, Message = "1", EventUnixTimeMs = 1 });
        queue.TryEnqueue(new LogRequestToApi { Source = "a", LogLevel = LogLevel.Information, Message = "2", EventUnixTimeMs = 2 });

        var result = queue.TryEnqueue(new LogRequestToApi { Source = "a", LogLevel = LogLevel.Information, Message = "3", EventUnixTimeMs = 3 });

        result.Should().BeFalse(); // Son eklenen 3. log başarısız olmalı
    }
}
