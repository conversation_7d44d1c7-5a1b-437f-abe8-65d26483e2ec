﻿using Ardalis.Result;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Domain.Entities;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Application.Operations.Assignments
{
    public class AssignStudentToClass
    {
        public class Request : IRequest<Result<string>>
        {
            public string CourseClassId { get; set; } = null!;
            public string StudentId { get; set; } = null!;
            public ClassOperationType ClassOperationType { get; set; } = ClassOperationType.Join;
        }

        public class RequestValidator : AbstractValidator<Request>
        {
            public RequestValidator()
            {
                RuleFor(r => r.CourseClassId)
                .NotEmpty().WithMessage("CourseClassId is required.");
                RuleFor(r => r.StudentId)
                    .NotEmpty().WithMessage("StudentUserId is required.");
            }
        }

        public class Handler(ICrudService crudService, IValidator<Request> validator) : IRequestHandler<Request, Result<string>>
        {
            public async Task<Result<string>> Handle(Request request, CancellationToken cancellationToken)
            {
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = validationResult.Errors
                        .Select(x => new ValidationError
                        {
                            Identifier = x.PropertyName,
                            ErrorMessage = x.ErrorMessage,
                        }).ToList();

                    return Result.Invalid(validationErrors);
                }

                var courseClass = await crudService.GetByIdAsync<CourseClassEntity>(request.CourseClassId, cancellationToken);

                if (courseClass is null)
                {
                    return Result.NotFound("Class not found.");
                }

                var participation = await crudService.GetAll<ClassParticipationEntity>()
                    .Where(x => x.UserId == request.StudentId
                        && x.CourseClassId == request.CourseClassId
                        && x.ClassParticipationType == ClassParticipationType.Student)
                    .OrderByDescending(x => x.OperationDate)
                    .FirstOrDefaultAsync(cancellationToken);

                if (participation is not null && participation.ClassOperationType == request.ClassOperationType)
                {
                    return Result.Conflict("The student is already assigned with the same operation type.");
                }

                participation = new ClassParticipationEntity()
                {
                    CourseClassId = request.CourseClassId,
                    UserId = request.StudentId,
                    ClassOperationType = request.ClassOperationType,
                    ClassParticipationType = ClassParticipationType.Student,
                    OperationDate = DateTime.UtcNow
                };

                await crudService.AddAsync(participation, cancellationToken);

                var result = Result.Success(participation.Id);

                return result;
            }
        }
    }
}