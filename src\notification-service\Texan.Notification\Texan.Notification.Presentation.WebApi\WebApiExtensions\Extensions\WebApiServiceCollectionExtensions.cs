﻿using Texan.Notification.Infrastructure.DependencyInjection;
using Texan.Notification.Presentation.WebApi.WebApiExtensions.Middlewares;

namespace Texan.Notification.Presentation.WebApi.WebApiExtensions.Extensions
{
    public static class WebApiServiceCollectionExtensions
    {
        public static IServiceCollection AddAppServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddInfrastructureServices(configuration);
            services.AddTransient<ExceptionHandlingMiddleware>();
            services.AddJwtAuthentication(configuration);
            services.AddEndpointsApiExplorer();
            services.AddHttpClient();
            services.AddSwaggerGen();
            services.AddCors(options =>
            {
                options.AddPolicy("AllowAllOrigins", builder =>
                {
                    builder.AllowAnyOrigin()
                           .AllowAnyMethod()
                           .AllowAnyHeader();
                });
            });
            return services;
        }
    }
}
