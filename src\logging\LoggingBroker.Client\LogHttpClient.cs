﻿using LoggingBroker.Client.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.CircuitBreaker;
using System.Net.Http.Json;

namespace LoggingBroker.Client
{
    public class LogHttpClient : ILogClient
    {
        private readonly HttpClient _client;
        private readonly LoggingBrokerOptions _options;
        private readonly ILogger<LogHttpClient> _logger;
        private readonly AsyncCircuitBreakerPolicy _circuitBreaker;

        public LogHttpClient(
            HttpClient client,
            IOptions<LoggingBrokerOptions> options,
            ILogger<LogHttpClient> logger)
        {
            _client = client;
            _options = options.Value;
            _logger = logger;

            _client.BaseAddress = new Uri(_options.BaseUrl);
            _client.Timeout = TimeSpan.FromSeconds(_options.HttpClientTimeoutSeconds);

            _circuitBreaker = Policy
                .Handle<HttpRequestException>()
                .Or<TaskCanceledException>()
                .AdvancedCircuitBreakerAsync(
                failureThreshold: _options.CircuitBreakerFailureRate,
                samplingDuration: TimeSpan.FromSeconds(_options.CircuitBreakerSamplingSeconds),
                minimumThroughput: _options.CircuitBreakerMinimumThroughput,
                durationOfBreak: TimeSpan.FromSeconds(_options.CircuitBreakerBreakDurationSeconds),
                onBreak: (ex, ts) => _logger.LogWarning(ex,
                    "Circuit broken for {BreakDuration}.", ts),
                onReset: () => _logger.LogInformation("Circuit reset."),
                onHalfOpen: () => _logger.LogInformation("Circuit half-open.")
                );
        }

        public async Task SendBatchAsync(IEnumerable<LogRequestToApi> batch, CancellationToken cancellationToken)
        {
            await _circuitBreaker.ExecuteAsync(async ct =>
            {
                // TODO: Enum string serialization for LogLevel ? 
                var response = await _client.PostAsJsonAsync(_options.LogEndpoint, batch, ct);
                response.EnsureSuccessStatusCode();
            }, cancellationToken);
        }
    }
}
