namespace Texan.Grade.Application.Operations.Grade.Commands;

public class UpdateGradeCommand
{
    public class Request : IRequest<Result<GradeEntity>>
    {
        public Guid Id { get; set; }
        public decimal Grade { get; set; }
        public string GivenByUserId { get; set; } = string.Empty;
        public bool IsFinal { get; set; } = false;
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Id)
                .NotEmpty().WithMessage("Grade ID is required.");
            
            RuleFor(r => r.GivenByUserId)
                .NotEmpty().WithMessage("Instructor ID is required.");
            
            RuleFor(r => r.Grade)
                .GreaterThanOrEqualTo(0).WithMessage("Grade must be greater than or equal to 0.")
                .LessThanOrEqualTo(100).WithMessage("Grade must be less than or equal to 100.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<GradeEntity>>
    {
        public async Task<Result<GradeEntity>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Updating grade: {GradeId}", request.Id);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Grade update failed validation: {GradeId}", request.Id);
                return validationError;
            }

            var grade = await crudService
                .Query<GradeEntity>()
                .FirstOrDefaultAsync(g => g.Id == request.Id, cancellationToken);

            if (grade is null)
            {
                logger.LogWarning("Grade not found for update: {GradeId}", request.Id);
                return Result.NotFound("Grade not found.");
            }

            if (grade.IsFinal)
            {
                logger.LogWarning("Cannot update finalized grade: {GradeId}", request.Id);
                var error = new ValidationError
                {
                    Identifier = nameof(request.Id),
                    ErrorMessage = "Cannot update a finalized grade."
                };
                return Result.Invalid(error);
            }
        
            grade.Grade = request.Grade;
            grade.GivenByUserId = request.GivenByUserId;
            grade.IsFinal = request.IsFinal;

            await crudService.UpdateAsync(grade, cancellationToken);
        
            logger.LogInformation("Grade updated: {GradeId}", grade.Id);
            return Result.Success(grade);
        }
    }
} 