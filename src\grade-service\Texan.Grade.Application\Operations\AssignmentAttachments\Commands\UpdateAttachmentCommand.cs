namespace Texan.Grade.Application.Operations.AssignmentAttachments.Commands;

public class UpdateAttachmentCommand
{
    public class Request : IRequest<Result<AssignmentAttachmentEntity>>
    {
        public Guid Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Id)
                .NotEmpty().WithMessage("Attachment ID is required.");
            
            RuleFor(r => r.FileName)
                .NotEmpty().WithMessage("File name is required.")
                .MaximumLength(255).WithMessage("File name cannot exceed 255 characters.");
            
            RuleFor(r => r.FileType)
                .NotEmpty().WithMessage("File type is required.")
                .MaximumLength(50).WithMessage("File type cannot exceed 50 characters.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<AssignmentAttachmentEntity>>
    {
        public async Task<Result<AssignmentAttachmentEntity>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Updating attachment: {AttachmentId}", request.Id);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
                return validationError;

            var attachment = await crudService
                .Query<AssignmentAttachmentEntity>()
                .FirstOrDefaultAsync(aa => aa.Id == request.Id, cancellationToken);

            if (attachment is null)
            {
                return Result.NotFound("Attachment not found.");
            }
        
            attachment.FileName = request.FileName;
            attachment.FileType = request.FileType;

            await crudService.UpdateAsync(attachment, cancellationToken);
        
            logger.LogInformation("Attachment updated: {AttachmentId}", attachment.Id);
            return Result.Success(attachment);
        }
    }
}