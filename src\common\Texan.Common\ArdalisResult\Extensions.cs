﻿using Ardalis.Result;
using System.Net;
using System.Text.Json;
using Texan.Common.JsonSerializer;

namespace Texan.Common.ArdalisResult;

public static class ResultExtensions
{
    public static Result<T> ToArdalisResult<T>(this T value, string? errorMessage = null)
    {
        if (value is null)
        {
            return Result<T>.Error(errorMessage ?? "Value cannot be null.");
        }
        return Result<T>.Success(value);
    }

    public static Result<IEnumerable<T>> ToArdalisResult<T>(this IEnumerable<T> values, string? errorMessage = null)
    {
        if (values == null || !values.Any())
        {
            return Result<IEnumerable<T>>.Error(errorMessage ?? "Collection cannot be null or empty.");
        }
        return Result.Success(values);
    }

    public static async Task<Result> ToArdalisResultAsync(this HttpResponseMessage response, CancellationToken cancellationToken = default)
    {
        string content = string.Empty;
        if (response.StatusCode == HttpStatusCode.NoContent)
        {
            return Result.NoContent();
        }

        if (response.Content.Headers.ContentLength > 0)
        {
            using var sr = await response.Content.ReadAsStreamAsync(cancellationToken);
            sr.Position = 0; // Reset the stream position to the beginning
            using var reader = new StreamReader(sr, leaveOpen: true);
            content = await reader.ReadToEndAsync(cancellationToken) ?? string.Empty;
            sr.Position = 0; // Reset the stream position again for deserialization
        }

        if (!response.IsSuccessStatusCode)
        {
            var errorResult = response.StatusCode switch
            {
                HttpStatusCode.NotFound => Result.NotFound(content),
                HttpStatusCode.BadRequest => Result.Invalid(new ValidationError(content)),
                HttpStatusCode.Unauthorized => Result.Unauthorized(content),
                HttpStatusCode.Forbidden => Result.Forbidden(content),
                HttpStatusCode.Conflict => Result.Conflict(content),
                HttpStatusCode.InternalServerError => Result.Error(content),
                _ => Result.CriticalError(content),
            };

            return errorResult;
        }

        if (response.StatusCode == HttpStatusCode.Created)
        {
            var location = response.Headers.Location?.ToString() ?? string.Empty;
            return location != null
                ? Result.Created(default, location)
                : Result.Created(default);
        }

        Result result = response.StatusCode switch
        {
            HttpStatusCode.OK => Result.Success(),
            HttpStatusCode.NoContent => Result.NoContent(),
            _ => Result.Success()
        };

        return result;
    }

    public static async Task<Result<T>> ToArdalisResultAsync<T>(this HttpResponseMessage response,
        IJsonSerializer? jsonSerializer = null,
        JsonSerializerOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        string content = string.Empty;
        if (response.StatusCode == HttpStatusCode.NoContent)
        {
            return Result.NoContent();
        }

        if (response.Content.Headers.ContentLength > 0)
        {
            using var sr = await response.Content.ReadAsStreamAsync(cancellationToken);
            sr.Position = 0; // Reset the stream position to the beginning
            using var reader = new StreamReader(sr, leaveOpen: true);
            content = await reader.ReadToEndAsync(cancellationToken) ?? string.Empty;
            sr.Position = 0; // Reset the stream position again for deserialization
        }

        if (!response.IsSuccessStatusCode)
        {
            var errorResult = response.StatusCode switch
            {
                HttpStatusCode.NotFound => Result<T>.NotFound(content),
                HttpStatusCode.BadRequest => Result<T>.Invalid(new ValidationError(content)),
                HttpStatusCode.Unauthorized => Result<T>.Unauthorized(content),
                HttpStatusCode.Forbidden => Result<T>.Forbidden(content),
                HttpStatusCode.Conflict => Result<T>.Conflict(content),
                HttpStatusCode.InternalServerError => Result<T>.Error(content),
                _ => Result<T>.CriticalError(content),
            };
            return errorResult;
        }

        if (response.StatusCode == HttpStatusCode.NoContent)
        {
            return Result.NoContent();
        }

        var body = jsonSerializer != null
            ? jsonSerializer.Deserialize<T>(content, options ?? JsonSerializerOptions.Web)
            : System.Text.Json.JsonSerializer.Deserialize<T>(content, options ?? JsonSerializerOptions.Web);
        if (body is null)
        {
            return Result.Error("Json deserialization failed.");
        }
        if (response.StatusCode == HttpStatusCode.Created)
        {
            var location = response.Headers.Location?.ToString() ?? string.Empty;
            return location != null
                ? Result.Created(body, location)
                : Result.Created(body);
        }

        return Result.Success(body);
    }
}