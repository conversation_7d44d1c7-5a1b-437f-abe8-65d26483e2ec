﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Texan.AuthBroker.Infrastructure.Keycloak;

namespace Texan.AuthBroker.Infrastructure;

public static class InfrastructureServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services,
        IConfiguration configuration)
    {
        var baseUrl = configuration["Keycloak:BaseUrl"];
        if (string.IsNullOrEmpty(baseUrl))
        {
            throw new ArgumentException("The required configuration value 'Keycloak:BaseUrl' is missing or empty.", nameof(configuration));
        }

        services.AddHttpClient<IKeycloakAdminClient, KeycloakAdminClient>(client =>
        {
            client.BaseAddress = new Uri(baseUrl);
        });

        return services;
    }
}