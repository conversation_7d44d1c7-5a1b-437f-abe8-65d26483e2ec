﻿using Ardalis.Result;
using FluentValidation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net.Http.Json;
using Texan.AuthBroker.Application.DTOs;
using Texan.AuthBroker.Domain.Entities;
using Texan.AuthBroker.Infrastructure.Keycloak.Models;

namespace Texan.AuthBroker.Infrastructure.Keycloak;

internal class KeycloakAdminClient : IKeycloakAdminClient
{
    private readonly ILogger? _logger;
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _config;
    private readonly IValidator<TokenRequest> _tokenRequestValidator;
    private readonly IValidator<CreateUserRequest> _createUserValidator;
    private readonly IValidator<CustomClaimRequest> _customClaimValidator;

    public KeycloakAdminClient(ILogger<KeycloakAdminClient> logger, HttpClient httpClient, IConfiguration config, IValidator<TokenRequest> tokenRequestValidator, IValidator<CreateUserRequest> createUserValidator, IValidator<CustomClaimRequest> customClaimValidator)
    {
        _logger = logger;
        _httpClient = httpClient;
        _config = config;
        _tokenRequestValidator = tokenRequestValidator;
        _createUserValidator = createUserValidator;
        _customClaimValidator = customClaimValidator;
    }

    private async Task<string> GetAdminTokenAsync()
    {
        var adminUser = _config["Keycloak:AdminUser"] ?? "admin";
        var adminPass = _config["Keycloak:AdminPass"] ?? "admin";
        var masterRealm = "master";

        var tokenUrl = $"{_httpClient.BaseAddress}realms/{masterRealm}/protocol/openid-connect/token";

        const int maxRetry = 10;
        const int delayMs = 4000; // 4 saniye

        for (int attempt = 1; attempt <= maxRetry; attempt++)
        {
            try
            {
                var req = new HttpRequestMessage(HttpMethod.Post, tokenUrl)
                {
                    Content = new FormUrlEncodedContent(new Dictionary<string, string>
                    {
                        ["grant_type"] = "password",
                        ["client_id"] = "admin-cli",
                        ["username"] = adminUser,
                        ["password"] = adminPass
                    })
                };

                var resp = await _httpClient.SendAsync(req);
                resp.EnsureSuccessStatusCode();

                var json = await resp.Content.ReadFromJsonAsync<Dictionary<string, object>>();
                return json["access_token"].ToString();
            }
            catch (Exception)
            {
                if (attempt == maxRetry)
                    throw new Exception("❌ Keycloak admin token alınamadı. Max retry denendi.");

                //TODO: ILogger imp sonrası güncellenecek.
                Console.WriteLine($"🔁 Token alınamadı. {attempt}. deneme. {delayMs / 1000} saniye bekleniyor...");
                await Task.Delay(delayMs);
            }
        }
        throw new Exception("❌ Keycloak admin token alınamadı. Tüm denemeler başarısız.");
    }

    private async Task<HttpClient> WithTokenAsync()
    {
        var token = await GetAdminTokenAsync();
        _httpClient.DefaultRequestHeaders.Remove("Authorization");
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");
        return _httpClient;
    }

    public async Task<Result<string>> CreateUserAsync(CreateUserRequest request)
    {
        _logger?.LogInformation("CreateUserAsync started for Username={Username}", request.Username);

        try
        {
            var validationResult = await _createUserValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                _logger?.LogWarning("Validation failed in CreateUserAsync for Username={Username}. Errors: {Errors}",
                    request.Username,
                    string.Join(", ", validationResult.Errors.Select(e => $"{e.PropertyName}: {e.ErrorMessage}")));

                return Result<string>.Invalid(validationResult.Errors.Select(e =>
                    new ValidationError(e.PropertyName, e.ErrorMessage)));
            }

            var realm = _config["Keycloak:Realm"] ?? "texan";
            var client = await WithTokenAsync();
            var url = $"admin/realms/{realm}/users";

            var userBody = new
            {
                username = request.Username,
                email = request.Email,
                firstName = request.FirstName,
                lastName = request.LastName,
                enabled = request.Enabled
            };

            var response = await client.PostAsJsonAsync(url, userBody);
            response.EnsureSuccessStatusCode();

            var location = response.Headers.Location;
            var segments = location.Segments;
            var userId = segments[^1];

            _logger?.LogInformation("User created successfully. Username={Username}, UserId={UserId}", request.Username, userId);

            if (!string.IsNullOrEmpty(request.InitialPassword))
            {
                var pwdResult = await ResetUserPasswordAsync(userId, request.InitialPassword);
                if (pwdResult.IsSuccess)
                {
                    _logger?.LogInformation("Initial password set successfully for UserId={UserId}", userId);
                }
                else
                {
                    _logger?.LogWarning("Initial password could not be set for UserId={UserId}", userId);
                }
            }

            return Result.Success(userId);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "CreateUserAsync failed for Username={Username}: {Message}", request.Username, ex.Message);
            return Result<string>.Error("Kullanıcı oluşturulurken hata oluştu.");
        }
    }

    public async Task<Result> CreateRoleAsync(string roleName)
    {
        _logger?.LogInformation("CreateRoleAsync started for RoleName={RoleName}", roleName);

        try
        {
            var realm = _config["Keycloak:Realm"] ?? "texan";
            var client = await WithTokenAsync();
            var url = $"admin/realms/{realm}/roles";

            var body = new { name = roleName };
            var resp = await client.PostAsJsonAsync(url, body);
            resp.EnsureSuccessStatusCode();

            _logger?.LogInformation("Role created successfully. RoleName={RoleName}", roleName);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "CreateRoleAsync failed for RoleName={RoleName}: {Message}", roleName, ex.Message);
            return Result.Error("Rol oluşturulurken bir hata oluştu.");
        }
    }

    public async Task<Result> AssignRoleToUserAsync(string userId, string roleName)
    {
        _logger?.LogInformation("AssignRoleToUserAsync started for UserId={UserId}, RoleName={RoleName}", userId, roleName);

        try
        {
            var realm = _config["Keycloak:Realm"] ?? "texan";
            var client = await WithTokenAsync();

            var roleUrl = $"admin/realms/{realm}/roles/{roleName}";
            _logger?.LogDebug("Fetching role data from {RoleUrl}", roleUrl);

            var roleResp = await client.GetAsync(roleUrl);
            roleResp.EnsureSuccessStatusCode();
            var roleData = await roleResp.Content.ReadFromJsonAsync<KeycloakRoleDto>();

            _logger?.LogDebug("Role data fetched successfully. RoleId={RoleId}", roleData?.Id);

            var mappingUrl = $"admin/realms/{realm}/users/{userId}/role-mappings/realm";
            var body = new[] { new { id = roleData.Id, name = roleData.Name } };
            var assignResp = await client.PostAsJsonAsync(mappingUrl, body);
            assignResp.EnsureSuccessStatusCode();

            _logger?.LogInformation("Role assigned successfully to UserId={UserId}, RoleName={RoleName}", userId, roleName);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "AssignRoleToUserAsync failed for UserId={UserId}, RoleName={RoleName}: {Message}", userId, roleName, ex.Message);
            return Result.Error("Kullanıcıya rol atanırken hata oluştu.");
        }
    }

    public async Task<Result> SetUserActiveAsync(string userId, bool isActive)
    {
        _logger?.LogInformation("SetUserActiveAsync started for UserId={UserId}, IsActive={IsActive}", userId, isActive);

        try
        {
            var realm = _config["Keycloak:Realm"] ?? "texan";
            var client = await WithTokenAsync();

            var url = $"admin/realms/{realm}/users/{userId}";
            var body = new { enabled = isActive };

            _logger?.LogDebug("Sending PUT request to {Url} with body: enabled={IsActive}", url, isActive);

            var resp = await client.PutAsJsonAsync(url, body);
            resp.EnsureSuccessStatusCode();

            _logger?.LogInformation("User status updated successfully. UserId={UserId}, IsActive={IsActive}", userId, isActive);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "SetUserActiveAsync failed for UserId={UserId}, IsActive={IsActive}: {Message}", userId, isActive, ex.Message);
            return Result.Error("Kullanıcı aktif/pasif durumu güncellenemedi.");
        }
    }

    public async Task<Result> ResetUserPasswordAsync(string userId, string newPassword)
    {
        _logger?.LogInformation("ResetUserPasswordAsync started for UserId={UserId}", userId);

        try
        {
            var realm = _config["Keycloak:Realm"] ?? "texan";
            var client = await WithTokenAsync();

            var url = $"admin/realms/{realm}/users/{userId}/reset-password";
            var body = new { type = "password", value = newPassword, temporary = false };

            _logger?.LogDebug("Sending PUT request to {Url} for password reset", url);

            var resp = await client.PutAsJsonAsync(url, body);
            resp.EnsureSuccessStatusCode();

            _logger?.LogInformation("Password reset successful for UserId={UserId}", userId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "ResetUserPasswordAsync failed for UserId={UserId}: {Message}", userId, ex.Message);
            return Result.Error("Şifre sıfırlama işlemi başarısız.");
        }
    }

    public async Task<Result<bool>> CheckRealmExistsAsync(string realmName)
    {
        _logger?.LogInformation("CheckRealmExistsAsync started for RealmName={RealmName}", realmName);

        try
        {
            var client = await WithTokenAsync();
            var url = "admin/realms";

            _logger?.LogDebug("Sending GET request to {Url}", url);

            var realms = await client.GetFromJsonAsync<List<KeycloakRealmDto>>(url);

            if (realms == null)
            {
                _logger?.LogWarning("Realm list is null from Keycloak.");
                return Result<bool>.Error("Keycloak'tan realm listesi alınamadı.");
            }

            var exists = realms.Any(r => r.Realm == realmName);
            _logger?.LogInformation("Realm existence for {RealmName}: {Exists}", realmName, exists);

            return Result.Success(exists);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "CheckRealmExistsAsync failed for RealmName={RealmName}: {Message}", realmName, ex.Message);
            return Result<bool>.Error("Realm kontrolü sırasında bir hata oluştu.");
        }
    }

    public async Task<Result> CreateRealmAsync(Realm realm)
    {
        _logger?.LogInformation("CreateRealmAsync started for RealmName={RealmName}", realm.Name);

        try
        {
            var client = await WithTokenAsync();
            var url = "admin/realms";

            var body = new { realm = realm.Name, enabled = realm.IsActive };

            _logger?.LogDebug("Sending POST request to {Url} with payload: {@Payload}", url, body);

            var resp = await client.PostAsJsonAsync(url, body);
            resp.EnsureSuccessStatusCode();

            _logger?.LogInformation("Realm created successfully. RealmName={RealmName}", realm.Name);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "CreateRealmAsync failed for RealmName={RealmName}: {Message}", realm.Name, ex.Message);
            return Result.Error("Realm oluşturulurken bir hata oluştu.");
        }
    }

    public async Task<Result> CreateCustomClaimAsync(string claimName)
    {
        _logger?.LogInformation("CreateCustomClaimAsync started. ClaimName={ClaimName}", claimName);

        try
        {
            var realm = _config["Keycloak:Realm"] ?? "texan";
            var client = await WithTokenAsync();

            var configuredClientId = _config["Keycloak:ClientId"] ?? "test-client";
            var getCliUrl = $"admin/realms/{realm}/clients?clientId={configuredClientId}";

            _logger?.LogDebug("Fetching client with clientId={ClientId}", configuredClientId);
            var getCli = await client.GetAsync(getCliUrl);
            getCli.EnsureSuccessStatusCode();

            var list = await getCli.Content.ReadFromJsonAsync<List<KeycloakClientDto>>();
            if (list is null || list.Count == 0)
            {
                _logger?.LogWarning("Client not found: {ClientId}", configuredClientId);
                return Result.Error($"Client bulunamadı: '{configuredClientId}'");
            }

            var clientId = list[0].Id;
            var mapperUrl = $"admin/realms/{realm}/clients/{clientId}/protocol-mappers/models";

            var mapperBody = new
            {
                name = $"{claimName}-mapper",
                protocol = "openid-connect",
                protocolMapper = "oidc-usermodel-attribute-mapper",
                config = new Dictionary<string, string>
                {
                    ["user.attribute"] = claimName,
                    ["claim.name"] = claimName,
                    ["jsonType.label"] = "String",
                    ["id.token.claim"] = "true",
                    ["access.token.claim"] = "true",
                    ["userinfo.token.claim"] = "true"
                }
            };

            _logger?.LogDebug("Sending POST to {Url} with claim mapper: {@MapperBody}", mapperUrl, mapperBody);
            var resp = await client.PostAsJsonAsync(mapperUrl, mapperBody);
            resp.EnsureSuccessStatusCode();

            _logger?.LogInformation("Custom claim created successfully. ClaimName={ClaimName}", claimName);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "CreateCustomClaimAsync failed. ClaimName={ClaimName}, Error={Message}", claimName, ex.Message);
            return Result.Error("Custom claim oluşturulurken bir hata oluştu.");
        }
    }

    public async Task<Result> AssignCustomClaimToUserAsync(string userId, CustomClaimRequest request)
    {
        _logger?.LogInformation("AssignCustomClaimToUserAsync started. UserId={UserId}, Claim={ClaimName}", userId, request.ClaimName);

        try
        {
            var validationResult = await _customClaimValidator.ValidateAsync(request);
            if (!validationResult.IsValid)
            {
                _logger?.LogWarning("CustomClaimRequest validation failed for UserId={UserId}: {@Errors}", userId, validationResult.Errors);
                return Result.Invalid(validationResult.Errors.Select(e => new ValidationError(e.PropertyName, e.ErrorMessage)));
            }

            var realm = _config["Keycloak:Realm"] ?? "texan";
            var client = await WithTokenAsync();

            var url = $"admin/realms/{realm}/users/{userId}";
            var body = new
            {
                attributes = new Dictionary<string, string[]>
                {
                    [request.ClaimName] = new[] { request.ClaimValue }
                }
            };

            _logger?.LogDebug("Sending PUT to {Url} with body: {@Body}", url, body);
            var resp = await client.PutAsJsonAsync(url, body);
            resp.EnsureSuccessStatusCode();

            _logger?.LogInformation("Custom claim assigned successfully. UserId={UserId}, Claim={ClaimName}", userId, request.ClaimName);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "AssignCustomClaimToUserAsync failed. UserId={UserId}, Claim={ClaimName}, Error={Message}", userId, request.ClaimName, ex.Message);
            return Result.Error("Kullanıcıya custom claim atanırken bir hata oluştu.");
        }
    }

    public async Task<Result<bool>> CheckClientExistsAsync(string clientId)
    {
        _logger?.LogInformation("CheckClientExistsAsync started. clientId={ClientId}", clientId);

        try
        {
            var realm = _config["Keycloak:Realm"] ?? "texan";
            var client = await WithTokenAsync();

            var response = await client.GetAsync($"admin/realms/{realm}/clients?clientId={clientId}");
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                _logger?.LogError("Keycloak client check failed. StatusCode={StatusCode}, Body={Body}", response.StatusCode, error);
                return Result<bool>.Error("Client kontrolü başarısız oldu.");
            }

            var clients = await response.Content.ReadFromJsonAsync<List<KeycloakClientDto>>();
            if (clients is null)
            {
                _logger?.LogError("CheckClientExistsAsync response content is null. clientId={ClientId}", clientId);
                return Result<bool>.Error("Client listesi alınamadı.");
            }

            var exists = clients.Any();
            _logger?.LogInformation("CheckClientExistsAsync completed. clientId={ClientId}, exists={Exists}", clientId, exists);
            return Result.Success(exists);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "CheckClientExistsAsync error. clientId={ClientId}, message={Message}", clientId, ex.Message);
            return Result<bool>.Error("Client kontrolü sırasında bir hata oluştu.");
        }
    }

    public async Task<Result> CreateClientAsync(string clientId)
    {
        _logger?.LogInformation("CreateClientAsync started. ClientId={ClientId}", clientId);

        try
        {
            var realm = _config["Keycloak:Realm"] ?? "texan";
            var client = await WithTokenAsync();

            var clientBody = new
            {
                clientId = clientId,
                enabled = true,
                publicClient = true,
                redirectUris = new[] { "*" }
            };

            var url = $"admin/realms/{realm}/clients";
            _logger?.LogDebug("Sending POST to {Url} with body: {@Body}", url, clientBody);

            var response = await client.PostAsJsonAsync(url, clientBody);
            response.EnsureSuccessStatusCode();

            _logger?.LogInformation("Client created successfully. ClientId={ClientId}", clientId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "CreateClientAsync failed. ClientId={ClientId}, Error={Message}", clientId, ex.Message);
            return Result.Error("Client oluşturma sırasında hata oluştu.");
        }
    }

    public async Task<Result<TokenResponse>> LoginAsync(TokenRequest request, CancellationToken cancellationToken = default)
    {
        _logger?.LogInformation("LoginAsync started. Username={Username}", request.Username);

        var validationResult = await _tokenRequestValidator.ValidateAsync(request, cancellationToken);
        if (validationResult is not null && !validationResult.IsValid)
        {
            _logger?.LogWarning("Login validation failed for Username={Username}. Errors: {@Errors}",
                request.Username, validationResult.Errors.Select(e => e.ErrorMessage));

            return Result<TokenResponse>.Invalid(validationResult.Errors.Select(e => new ValidationError(e.PropertyName, e.ErrorMessage)));
        }

        try
        {
            var realm = _config["Keycloak:Realm"] ?? throw new InvalidOperationException("Realm is not configured.");
            var clientId = _config["Keycloak:ClientId"] ?? throw new InvalidOperationException("ClientId is not configured.");
            var tokenUrl = $"realms/{realm}/protocol/openid-connect/token";

            var tokenRequestBody = new FormUrlEncodedContent(new Dictionary<string, string>
            {
                ["grant_type"] = "password",
                ["client_id"] = clientId,
                ["username"] = request.Username,
                ["password"] = request.Password
            });

            _logger?.LogDebug("Sending login request to {TokenUrl} for Username={Username}", tokenUrl, request.Username);

            var response = await _httpClient.PostAsync(tokenUrl, tokenRequestBody, cancellationToken);
            if (!response.IsSuccessStatusCode)
            {
                var errorDetails = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger?.LogError("Login failed. StatusCode={StatusCode}, ReasonPhrase={ReasonPhrase}, ResponseBody={ResponseBody}",
                    response.StatusCode, response.ReasonPhrase, errorDetails);

                return Result<TokenResponse>.Unavailable("Login failed.");
            }

            var tokenResponse = await response.Content.ReadFromJsonAsync<TokenResponse>(cancellationToken: cancellationToken);
            if (tokenResponse is null)
            {
                _logger?.LogError("Token response is null for Username={Username}.", request.Username);
                return Result<TokenResponse>.Error("Invalid token response.");
            }

            _logger?.LogInformation("Login successful. Username={Username}, ExpiresIn={ExpiresIn}",
                request.Username, tokenResponse.ExpiresIn);

            return Result.Success(tokenResponse);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "LoginAsync encountered an unexpected error for Username={Username}: {Message}", request.Username, ex.Message);
            return Result<TokenResponse>.Error("Login işlemi sırasında beklenmeyen bir hata oluştu.");
        }
    }
}