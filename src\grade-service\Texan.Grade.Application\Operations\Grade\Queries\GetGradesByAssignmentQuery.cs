namespace Texan.Grade.Application.Operations.Grade.Queries;

public class GetGradesByAssignmentQuery
{
    public class Request : IRequest<Result<List<GradeEntity>>>
    {
        public Guid AssignmentId { get; set; }
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.AssignmentId)
                .NotEmpty().WithMessage("Assignment ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<List<GradeEntity>>>
    {
        public async Task<Result<List<GradeEntity>>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Getting grades for assignment: {AssignmentId}", request.AssignmentId);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Grades query failed validation for assignment: {AssignmentId}", request.AssignmentId);
                return validationError;
            }

            var assignmentExists = await crudService
                .Query<AssignmentEntity>()
                .AnyAsync(a => a.Id == request.AssignmentId, cancellationToken);

            if (!assignmentExists)
            {
                logger.LogWarning("Assignment not found for grades query: {AssignmentId}", request.AssignmentId);
                return Result.NotFound("Assignment not found.");
            }

            var grades = await crudService
                .Query<GradeEntity>()
                .Where(g => g.AssignmentId == request.AssignmentId)
                .OrderByDescending(g => g.CreatedAt)
                .ToListAsync(cancellationToken);

            logger.LogInformation("Retrieved {Count} grades for assignment: {AssignmentId}", grades.Count, request.AssignmentId);
            return Result.Success(grades);
        }
    }
}