﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Texan.ClassManagement.Domain.Entities
{
    public class RelRolePermissionEntity : BaseEntity
    {
        // TODO : To be modified

        // Foreign Keys
        public string RoleId { get; set; } = null!;

        public string PermissionId { get; set; } = null!;

        // Navigation
        [ForeignKey(nameof(RoleId))]
        public RoleEntity Role { get; set; } = null!;

        [ForeignKey(nameof(PermissionId))]
        public PermissionEntity Permission { get; set; } = null!;
    }
}