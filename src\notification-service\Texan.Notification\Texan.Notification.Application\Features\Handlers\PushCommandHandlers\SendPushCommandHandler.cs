﻿using Ardalis.Result;
using MediatR;
using Texan.Notification.Application.Features.Commands.PushCommands;
using Texan.Notification.Domain.Entities;

namespace Texan.Notification.Application.Features.Handlers.PushCommandHandlers
{
    public class SendPushCommandHandler(IPushService _service)
        : IRequestHandler<SendPushCommand, Result>
    {
        public async Task<Result> Handle(SendPushCommand request, CancellationToken cancellationToken)
        {
            var entity = new PushRequestEntity
            {
                DeviceToken = request.DeviceToken,
                Title = request.Title,
                Body = request.Body
            };
            return await _service.SendAsync(entity, cancellationToken);
        }
    }
}