namespace Texan.Grade.Presentation.WebApi.Endpoints;

/// <summary>
/// Grade API endpoints
/// </summary>
public static class GradeEndpoints
{
    public static void MapGradeEndpoints(this IEndpointRouteBuilder app, bool skipAuth = false)
    {
        var group = app.MapGroup("/api/grades")
            .WithTags("Grades");
        
        
        if (!skipAuth)
        {
            group.RequireAuthorization();
        }

        // POST /api/grades
        group.MapPost("/", async ([FromBody] CreateGradeCommand.Request request, IMediator mediator, CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.Created($"/api/grades/{result.Value}", result.Value);
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
            
            if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                return Results.BadRequest(result.ValidationErrors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("CreateGrade")
        .WithSummary("Create a new grade")
        .WithDescription("Creates a new grade for a student assignment")
        .Accepts<CreateGradeCommand.Request>("application/json")
        .Produces<string>(StatusCodes.Status201Created)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // GET /api/grades/{id}
        group.MapGet("/{id:guid}", async (Guid id, IMediator mediator, CancellationToken cancellationToken) =>
        {
            var request = new GetGradeByIdQuery.Request { Id = id };
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.Ok(result.Value);
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("GetGradeById")
        .WithSummary("Get grade by ID")
        .WithDescription("Retrieves a specific grade by its identifier")
        .Produces<GradeEntity>()
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // PUT /api/grades/{id}
        group.MapPut("/{id:guid}", async (Guid id, [FromBody] UpdateGradeCommand.Request request, IMediator mediator, CancellationToken cancellationToken) =>
        {
            request.Id = id; 
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.Ok(result.Value);
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
            
            if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                return Results.BadRequest(result.ValidationErrors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("UpdateGrade")
        .WithSummary("Update a grade")
        .WithDescription("Updates an existing grade if not finalized")
        .Accepts<UpdateGradeCommand.Request>("application/json")
        .Produces<GradeEntity>()
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // DELETE /api/grades/{id}
        group.MapDelete("/{id:guid}", async (Guid id, IMediator mediator, CancellationToken cancellationToken) =>
        {
            var request = new DeleteGradeCommand.Request { Id = id };
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.NoContent();
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
            
            if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                return Results.BadRequest(result.ValidationErrors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("DeleteGrade")
        .WithSummary("Delete a grade")
        .WithDescription("Deletes a grade if not finalized")
        .Produces(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // GET /api/grades/student/{studentId}
        group.MapGet("/student/{studentId}", async (string studentId, IMediator mediator, CancellationToken cancellationToken) =>
            {
                var request = new GetGradesByStudentQuery.Request { StudentId = studentId };
                var result = await mediator.Send(request, cancellationToken);
    
                if (result.IsSuccess)
                    return Results.Ok(result.Value);
    
                if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                    return Results.BadRequest(result.ValidationErrors);
        
                return Results.BadRequest(result.Errors);
            })
            .WithName("GetGradesByStudent")
            .WithSummary("Get grades by student")
            .WithDescription("Retrieves all grades for a specific student. Returns empty array if student has no grades yet.")
            .Produces<List<GradeEntity>>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized);

        // GET /api/grades/assignment/{assignmentId}
        group.MapGet("/assignment/{assignmentId:guid}", async (Guid assignmentId, IMediator mediator, CancellationToken cancellationToken) =>
            {
                var request = new GetGradesByAssignmentQuery.Request { AssignmentId = assignmentId };
                var result = await mediator.Send(request, cancellationToken);
    
                if (result.IsSuccess)
                    return Results.Ok(result.Value);
    
                if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                    return Results.NotFound(result.Errors);
        
                if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                    return Results.BadRequest(result.ValidationErrors);
        
                return Results.BadRequest(result.Errors);
            })
            .WithName("GetGradesByAssignment")
            .WithSummary("Get grades by assignment")
            .WithDescription("Retrieves all grades for a specific assignment. Returns empty array if assignment exists but has no grades yet.")
            .Produces<List<GradeEntity>>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized)
            .ProducesProblem(StatusCodes.Status404NotFound);

        // POST /api/grades/bulk
        group.MapPost("/bulk", async ([FromBody] CreateMultipleGradesCommand.Request request, IMediator mediator, CancellationToken cancellationToken) =>
            {
                var result = await mediator.Send(request, cancellationToken);
    
                if (result.IsSuccess)
                {
                    var bulkResult = result.Value;
                    if (bulkResult.Failed == 0)
                    {
                        return Results.Created("/api/grades/bulk", bulkResult);
                    }
                    if (bulkResult.Successful > 0)
                    {
                        return Results.Ok(bulkResult);
                    }
                    return Results.BadRequest(bulkResult);
                }
    
                if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                    return Results.BadRequest(result.ValidationErrors);
        
                return Results.BadRequest(result.Errors);
            })
            .WithName("CreateMultipleGrades")
            .WithSummary("Create multiple grades")
            .WithDescription("Creates multiple grades in a single operation. Supports partial success with detailed results.")
            .Accepts<CreateMultipleGradesCommand.Request>("application/json")
            .Produces<CreateMultipleGradesCommand.BulkCreateResult>(StatusCodes.Status201Created)
            .Produces<CreateMultipleGradesCommand.BulkCreateResult>()
            .ProducesProblem(StatusCodes.Status400BadRequest) 
            .ProducesProblem(StatusCodes.Status401Unauthorized);
    }
}