﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace Texan.Common.LoggingMiddlewares;

internal sealed class CalculateRequestTimeMiddleware(RequestDelegate next, ILogger<CalculateRequestTimeMiddleware> logger)
{
    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        await next(context);
        stopwatch.Stop();
        double elapsedMilliseconds = (double)stopwatch.ElapsedTicks / Stopwatch.Frequency * 1000;
        logger.LogInformation("Request completed in {ElapsedMilliseconds:F3}ms. Trace Id: {RequestTraceId}", elapsedMilliseconds, context.TraceIdentifier);
    }
}