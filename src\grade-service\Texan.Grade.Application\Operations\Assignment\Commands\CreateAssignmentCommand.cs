

namespace Texan.Grade.Application.Operations.Assignment.Commands;

public class CreateAssignmentCommand
{
    public class Request : IRequest<Result<string>>
    {
        public string Name { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public string CourseClassId { get; set; } = string.Empty;
        public string? WeekId { get; set; }
        public byte Order { get; set; }
        public DateTime AssignedAt { get; set; }
        public DateTime? DueDate { get; set; }
        public string CreatedByUserId { get; set; } = string.Empty;
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Name)
                .NotEmpty().WithMessage("Assignment name is required.")
                .MaximumLength(200).WithMessage("Assignment name cannot exceed 200 characters.");
            
            RuleFor(r => r.Details)
                .NotEmpty().WithMessage("Assignment details are required.")
                .MaximumLength(2000).WithMessage("Assignment details cannot exceed 2000 characters.");
            
            RuleFor(r => r.CourseClassId)
                .NotEmpty().WithMessage("Course Class ID is required.");
            
            RuleFor(r => r.CreatedByUserId)
                .NotEmpty().WithMessage("Instructor ID is required.");
            
            RuleFor(r => r.AssignedAt)
                .NotEmpty().WithMessage("Assignment date is required.");
            
            RuleFor(r => r.DueDate)
                .GreaterThan(r => r.AssignedAt)
                .When(r => r.DueDate.HasValue)
                .WithMessage("Due date must be after assignment date.");
        }
    }
    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<string>>
    {
        public async Task<Result<string>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Creating assignment: {Name}", request.Name);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Assignment creation failed validation: {Name}", request.Name);
                return validationError;
            }
        
            var existingAssignment = await crudService
                .Query<AssignmentEntity>()
                .FirstOrDefaultAsync(a => a.Name == request.Name && a.CourseClassId == request.CourseClassId, cancellationToken);

            if (existingAssignment is not null)
            {
                logger.LogWarning("Assignment creation failed - duplicate name: {Name} in course {CourseClassId}", request.Name, request.CourseClassId);
                var error = new ValidationError
                {
                    Identifier = nameof(request.Name),
                    ErrorMessage = "An assignment with this name already exists in the course class."
                };
                return Result.Invalid(error);
            }

            var assignment = new AssignmentEntity
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                Details = request.Details,
                CourseClassId = request.CourseClassId,
                WeekId = request.WeekId,
                Order = request.Order,
                AssignedAt = request.AssignedAt,
                DueDate = request.DueDate,
                CreatedByUserId = request.CreatedByUserId
            };

            await crudService.AddAsync(assignment, cancellationToken);
        
            logger.LogInformation("Assignment created: {AssignmentId}", assignment.Id);
            return Result.Success(assignment.Id.ToString());
        }
    }
}