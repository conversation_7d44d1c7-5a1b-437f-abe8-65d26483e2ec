﻿using System.Text.Json;

namespace Texan.Common.JsonSerializer;

internal class SystemTextJsonSerializer(JsonSerializerOptions jsonSerializerOptions) : IJsonSerializer
{
    private readonly JsonSerializerOptions _jsonSerializerOptions = jsonSerializerOptions ?? throw new ArgumentNullException(nameof(jsonSerializerOptions));

    public T? Deserialize<T>(string json, JsonSerializerOptions? serializerOptions = null)
    {
        return System.Text.Json.JsonSerializer.Deserialize<T>(json, serializerOptions ?? _jsonSerializerOptions);
    }

    public string Serialize<T>(T obj, JsonSerializerOptions? serializerOptions = null)
    {
        return System.Text.Json.JsonSerializer.Serialize(obj, serializerOptions ?? _jsonSerializerOptions);
    }
}