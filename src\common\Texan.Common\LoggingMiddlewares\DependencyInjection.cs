﻿using Microsoft.AspNetCore.Builder;

namespace Texan.Common.LoggingMiddlewares;

public static class DependencyInjection
{
    public static IApplicationBuilder UseCalculateRequestTimeMiddleware(this IApplicationBuilder app)
    {
        app.UseMiddleware<CalculateRequestTimeMiddleware>();
        return app;
    }

    public static IApplicationBuilder UseErrorLoggingMiddleware(this IApplicationBuilder app)
    {
        app.UseMiddleware<ErrorLoggingMiddleware>();
        return app;
    }

    public static IApplicationBuilder UseRequestLoggingMiddleware(this IApplicationBuilder app)
    {
        app.UseMiddleware<RequestLoggingMiddleware>();
        return app;
    }
}