services:
  gateway-service:
    build:
      context: ./Texan.Gateway.Api
      dockerfile: Dockerfile
    container_name: gateway-service
    ports:
      - "5001:8080"
    environment:
      - ASPNETCORE_URLS=http://+:8080
      - ASPNETCORE_ENVIRONMENT=Development
    volumes:
      - ./Texan.Gateway.Api/AppConfiguration:/app/AppConfiguration  
    networks:
      - texan-network
networks:
  texan-network:
    external: true
