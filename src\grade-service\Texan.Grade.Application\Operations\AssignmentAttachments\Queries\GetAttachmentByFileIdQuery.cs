namespace Texan.Grade.Application.Operations.AssignmentAttachments.Queries;

public class GetAttachmentByFileIdQuery
{
    public class Request : IRequest<Result<AssignmentAttachmentEntity>>
    {
        public string FileId { get; set; } = string.Empty;
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.FileId)
                .NotEmpty().WithMessage("File ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<AssignmentAttachmentEntity>>
    {
        public async Task<Result<AssignmentAttachmentEntity>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Getting attachment by file ID: {FileId}", request.FileId);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Attachment query failed validation for file ID: {FileId}", request.FileId);
                return validationError;
            }

            var attachment = await crudService
                .Query<AssignmentAttachmentEntity>()
                .FirstOrDefaultAsync(aa => aa.FileId == request.FileId, cancellationToken);

            if (attachment is null)
            {
                logger.LogWarning("Attachment not found by file ID: {FileId}", request.FileId);
                return Result.NotFound("Attachment not found.");
            }

            logger.LogInformation("Attachment retrieved by file ID: {FileId}", request.FileId);
            return Result.Success(attachment);
        }
    }
}