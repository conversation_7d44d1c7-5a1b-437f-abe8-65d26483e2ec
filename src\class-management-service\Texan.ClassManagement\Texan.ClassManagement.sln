﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35728.132
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.ClassManagement.Presentation.WebApi", "Texan.ClassManagement.Presentation.WebApi\Texan.ClassManagement.Presentation.WebApi.csproj", "{7C0DE2A1-C801-4778-81A6-21A72443D3D1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.ClassManagement.Application", "Texan.ClassManagement.Application\Texan.ClassManagement.Application.csproj", "{F241582C-A5D9-4EA0-912F-A9D7A6DE7471}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.ClassManagement.Domain", "Texan.ClassManagement.Domain\Texan.ClassManagement.Domain.csproj", "{D15B2BA6-E4AA-4B39-B4CA-8D9848981068}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.ClassManagement.Infrastructure", "Texan.ClassManagement.Infrastructure\Texan.ClassManagement.Infrastructure.csproj", "{5ABB4928-E895-4275-9176-4931D2374EDE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Texan.ClassManagement.Tests", "..\..\..\tests\class-management\Texan.ClassManagement.Tests\Texan.ClassManagement.Tests.csproj", "{011E31A5-DEC4-CD95-CEA1-8A47B0C3DEB9}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7C0DE2A1-C801-4778-81A6-21A72443D3D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7C0DE2A1-C801-4778-81A6-21A72443D3D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7C0DE2A1-C801-4778-81A6-21A72443D3D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7C0DE2A1-C801-4778-81A6-21A72443D3D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{F241582C-A5D9-4EA0-912F-A9D7A6DE7471}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F241582C-A5D9-4EA0-912F-A9D7A6DE7471}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F241582C-A5D9-4EA0-912F-A9D7A6DE7471}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F241582C-A5D9-4EA0-912F-A9D7A6DE7471}.Release|Any CPU.Build.0 = Release|Any CPU
		{D15B2BA6-E4AA-4B39-B4CA-8D9848981068}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D15B2BA6-E4AA-4B39-B4CA-8D9848981068}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D15B2BA6-E4AA-4B39-B4CA-8D9848981068}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D15B2BA6-E4AA-4B39-B4CA-8D9848981068}.Release|Any CPU.Build.0 = Release|Any CPU
		{5ABB4928-E895-4275-9176-4931D2374EDE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5ABB4928-E895-4275-9176-4931D2374EDE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5ABB4928-E895-4275-9176-4931D2374EDE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5ABB4928-E895-4275-9176-4931D2374EDE}.Release|Any CPU.Build.0 = Release|Any CPU
		{011E31A5-DEC4-CD95-CEA1-8A47B0C3DEB9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{011E31A5-DEC4-CD95-CEA1-8A47B0C3DEB9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{011E31A5-DEC4-CD95-CEA1-8A47B0C3DEB9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{011E31A5-DEC4-CD95-CEA1-8A47B0C3DEB9}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {340CB64A-7EB9-4367-9A4C-1D08D5C17619}
	EndGlobalSection
EndGlobal
