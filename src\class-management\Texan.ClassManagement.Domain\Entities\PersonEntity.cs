﻿using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Domain.Entities
{
    public class PersonEntity
    {
        public string Name { get; set; } = null!;
        public string Surname { get; set; } = null!;
        public string Email { get; set; } = null!;
        public string Phone { get; set; } = null!;
        public Gender Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public AddressEntity Address { get; set; } = null!;
    }
}