

using Microsoft.EntityFrameworkCore.Storage;

namespace Texan.Grade.Application.Abstractions;

/// <summary>
/// Generic CRUD service interface for basic database operations
/// </summary>
public interface ICrudService
{
    /// <summary>
    /// Gets all entities of type T as queryable
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <returns>Queryable collection of entities</returns>
    IQueryable<T> GetAll<T>() where T : class;
    
    /// <summary>
    /// Gets a queryable for complex queries
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <returns>Queryable for building complex queries</returns>
    IQueryable<T> Query<T>() where T : class;
    
    /// <summary>
    /// Gets an entity by its identifier
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="id">Entity identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Entity if found, null otherwise</returns>
    Task<T?> GetByIdAsync<T>(Guid id, CancellationToken cancellationToken = default) where T : class;
    
    /// <summary>
    /// Adds a new entity
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="entity">Entity to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Added entity</returns>
    Task<T> AddAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class;
    
    /// <summary>
    /// Adds multiple entities
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="entities">Entities to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Added entities</returns>
    Task<T[]> AddMultipleAsync<T>(T[] entities, CancellationToken cancellationToken = default) where T : class;
    
    /// <summary>
    /// Updates an existing entity
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="entity">Entity to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated entity</returns>
    Task<T> UpdateAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class;
    
    /// <summary>
    /// Updates multiple entities
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="entities">Entities to update</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated entities</returns>
    Task<T[]> UpdateMultipleAsync<T>(T[] entities, CancellationToken cancellationToken = default) where T : class;
    
    /// <summary>
    /// Deletes an entity
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="entity">Entity to delete</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task DeleteAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class;
    
    /// <summary>
    /// Deletes multiple entities
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    /// <param name="entities">Entities to delete</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task DeleteMultipleAsync<T>(T[] entities, CancellationToken cancellationToken = default) where T : class;
    
    /// <summary>
    /// Gets a database transaction for complex operations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Database transaction</returns>
    Task<IDbContextTransaction> GetTransactionAsync(CancellationToken cancellationToken = default);
}