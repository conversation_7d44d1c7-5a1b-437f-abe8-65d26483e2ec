﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Texan.AuthBroker.Domain.Entities;
using Texan.AuthBroker.Infrastructure.Keycloak;

namespace Texan.AuthBroker.Infrastructure;

public static class ApplicationInitializationExtensions
{
    public static void InitializeRealm(this WebApplication app)
    {
        app.Lifetime.ApplicationStarted.Register(async () =>
        {
            using var scope = app.Services.CreateScope();
            var keycloak = scope.ServiceProvider.GetRequiredService<IKeycloakAdminClient>();
            var config = scope.ServiceProvider.GetRequiredService<IConfiguration>();

            var realmName = config["Keycloak:Realm"];
            var clientId = config["Keycloak:ClientId"];

            if (string.IsNullOrWhiteSpace(realmName))
                throw new InvalidOperationException("Realm name must be provided via environment variables.");

            if (string.IsNullOrWhiteSpace(clientId))
                throw new InvalidOperationException("ClientId must be provided via environment variables.");

            var realmExists = await keycloak.CheckRealmExistsAsync(realmName);
            if (!realmExists)
            {
                await keycloak.CreateRealmAsync(new Realm(realmName));
                Console.WriteLine($"✅ Realm '{realmName}' created.");
            }
            else
            {
                Console.WriteLine($"✅ Realm '{realmName}' already exists.");
            }

            var clientExists = await keycloak.CheckClientExistsAsync(clientId);
            if (!clientExists)
            {
                await keycloak.CreateClientAsync(clientId);
                Console.WriteLine($"✅ Client '{clientId}' created.");
            }
            else
            {
                Console.WriteLine($"✅ Client '{clientId}' already exists.");
            }

            var roles = RequiredRoleProvider.GetRoles(config);
            if (roles.Count > 0)
            {
                var roleResult = await keycloak.EnsureRolesExistAsync(roles);
                if (roleResult.IsSuccess)
                   Console.WriteLine("✅ Required roles are present / created.");
               else
                    Console.WriteLine($"⚠️  Role init failed: {roleResult.Errors.FirstOrDefault()}");
            }
           else
           {
               Console.WriteLine("ℹ️  No 'Keycloak:RequiredRoles' supplied; skipping role init.");
           }
        });
    }
}
