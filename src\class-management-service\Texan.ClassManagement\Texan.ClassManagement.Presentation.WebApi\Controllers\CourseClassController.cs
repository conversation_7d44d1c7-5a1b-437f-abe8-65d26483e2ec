﻿using Ardalis.Result.AspNetCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Operations.CourseClass.Commands;
using Texan.ClassManagement.Application.Operations.CourseClass.Queries;

namespace Texan.ClassManagement.Presentation.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "coordination-personnel, coordination-manager, admin, sysadmin")]
    public class CourseClassController : ControllerBase
    {
        private readonly IMediator _mediator;

        public CourseClassController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpGet]
        [TranslateResultToActionResult]
        public async Task<Ardalis.Result.IResult> GetAllCourseClasses(CancellationToken cancellationToken)
        {
            var request = new GetAllCourseClassesQuery.Request();
            var result = await _mediator.Send(request, cancellationToken);
            return result;
        }

        [HttpGet("{id}")]
        [TranslateResultToActionResult]
        public async Task<Ardalis.Result.IResult> GetCourseClassById(string id, CancellationToken cancellationToken)
        {
            var request = new GetCourseClassByIdQuery.Request { Id = id };
            var result = await _mediator.Send(request, cancellationToken);
            return result;
        }

        [HttpPost("create")]
        [TranslateResultToActionResult]
        public async Task<Ardalis.Result.IResult> CreateCourseClass([FromBody] CreateCourseClassCommand.Request request, CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(request, cancellationToken);
            return result;
        }

        [HttpPost("update-schedule")]
        [TranslateResultToActionResult]
        public async Task<Ardalis.Result.IResult> CreateOrUpdateCourseClassSchedule([FromBody] CreateOrUpdateCourseClassScheduleCommand.Request request, CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(request, cancellationToken);
            return result;
        }
    }
}
