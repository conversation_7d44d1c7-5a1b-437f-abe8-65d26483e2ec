﻿using Moq;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Application.Operations.CourseClass.Queries;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Tests.UnitTests.Application.CourseClass.Queries
{
    public class GetAllCourseClassesQueryTests
    {
        private readonly Mock<ICrudService> _mockCrudService;
        private readonly GetAllCourseClassesQuery.Handler _handler;

        public GetAllCourseClassesQueryTests()
        {
            _mockCrudService = new Mock<ICrudService>();
            _handler = new GetAllCourseClassesQuery.Handler(_mockCrudService.Object);
        }

        [Fact]
        public async Task Handle_WhenCourseClassesExist_ReturnsSuccessWithList()
        {
            // Arrange
            var courseClasses = new List<CourseClassEntity>
            {
                new CourseClassEntity { Id = "1", Name = "Class 1", CourseId = "C1" },
                new CourseClassEntity { Id = "2", Name = "Class 2", CourseId = "C2" }
            }.AsQueryable();

            _mockCrudService.Setup(x => x.GetAll<CourseClassEntity>())
                .Returns(courseClasses);

            // Act
            var result = await _handler.Handle(new GetAllCourseClassesQuery.Request(), CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(2, result.Value.Count);
            Assert.Contains(result.Value, c => c.Name == "Class 1");
            Assert.Contains(result.Value, c => c.Name == "Class 2");
        }

        [Fact]
        public async Task Handle_WhenNoCourseClassesExist_ReturnsSuccessWithEmptyList()
        {
            // Arrange
            var courseClasses = new List<CourseClassEntity>().AsQueryable();

            _mockCrudService.Setup(x => x.GetAll<CourseClassEntity>())
                .Returns(courseClasses);

            // Act
            var result = await _handler.Handle(new GetAllCourseClassesQuery.Request(), CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Empty(result.Value);
        }
    }
}
