﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace LoggingBroker.Client;

public class LoggingBrokerLoggerProvider(IOptions<LoggingBrokerOptions> options,
                                   LogQueueService queue) : ILoggerProvider
{
    private readonly LoggingBrokerOptions _options = options.Value;
    private readonly ConcurrentDictionary<string, LoggingBrokerLogger> _loggers = new();

    public ILogger CreateLogger(string categoryName)
    {
        return _loggers.GetOrAdd(categoryName,
            name => new LoggingBrokerLogger(name, queue, _options));
    }

    public void Dispose()
    {
        queue.Complete();
    }
}