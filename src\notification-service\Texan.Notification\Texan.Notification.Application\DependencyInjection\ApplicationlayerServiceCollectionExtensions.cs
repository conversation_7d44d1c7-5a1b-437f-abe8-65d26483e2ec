﻿using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using Texan.Notification.Application.Interfaces;

namespace Texan.Notification.Application.DependencyInjection
{
    public static class ApplicationlayerServiceCollectionExtensions
    {
        public static IServiceCollection AddApplicationLayer(this IServiceCollection services)
        {
            services.AddValidatorsFromAssemblyContaining<IAssemblyMarker>();

            services.AddMediatR(cfg =>
            {
                cfg.RegisterServicesFromAssemblyContaining<IAssemblyMarker>();
            });

            return services;
        }
    }
}