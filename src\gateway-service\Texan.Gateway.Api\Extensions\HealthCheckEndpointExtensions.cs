﻿using Microsoft.AspNetCore.Mvc;
using Texan.Gateway.Api.Enums;
using Texan.Gateway.Api.Services.Abstract;

namespace Texan.Gateway.Api.Extensions
{
    public static class HealthCheckEndpointExtensions
    {
        public static IEndpointRouteBuilder MapHealthCheckEndpoints(this IEndpointRouteBuilder endpoints)
        {
            endpoints.MapGet("/healthcheck/{target}", async ([FromRoute] HealthCheckTarget target, [FromServices] IHealthCheckApiService service) =>
            {
                var result = await service.CheckHealthAsync(target.GetSectionName(), target.ToString());
                return result;
            })
            .WithName("HealthCheck")
            .WithTags("Health");
            return endpoints;
        }
    }
}