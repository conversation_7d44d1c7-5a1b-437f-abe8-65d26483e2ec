﻿using FluentValidation;
using Texan.Notification.Application.Features.Commands.EmailCommands;
namespace Texan.Notification.Application.Validators
{
    public class MailAttachmentCommandValidator : AbstractValidator<MailAttachmentCommand>
    {
        public MailAttachmentCommandValidator()
        {
            RuleFor(x => x.FileName)
                .NotEmpty().WithMessage("FileName is required.")
                .MaximumLength(255).WithMessage("FileName must be at most 255 characters.");

            RuleFor(x => x.Content)
                .NotNull().WithMessage("Content is required.")
                .Must(c => c.Length > 0).WithMessage("Content cannot be empty.");

            RuleFor(x => x.ContentType)
                .NotEmpty().WithMessage("ContentType is required.")
                .MaximumLength(100).WithMessage("ContentType must be at most 100 characters.");
        }
    }
}
