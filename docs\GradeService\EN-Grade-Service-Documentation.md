# Grade Service API

## Overview

Grade Service is a RESTful API service that provides assignment and grade management for educational systems.

**Core Features:**
- .NET 8 Web API (Minimal APIs)
- CQ<PERSON> Pattern (MediatR)
- PostgreSQL Database
- JWT Authentication
- Docker Containerization
- Comprehensive Validation

## Quick Start

### Setup

```bash
# Start services
docker-compose up -d

# Check API status
curl http://localhost:8080/health

# Swagger UI
http://localhost:8080
```

### API Base URL
```
http://localhost:8080
```

## API Endpoints

### 📝 Assignments (8 endpoints)

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/assignments` | Create new assignment |
| `GET` | `/api/assignments/{id}` | Get assignment details |
| `PUT` | `/api/assignments/{id}` | Update assignment |
| `DELETE` | `/api/assignments/{id}` | Delete assignment |
| `GET` | `/api/assignments/course/{courseClassId}` | List course assignments |
| `GET` | `/api/assignments/week/{weekId}` | List week assignments |
| `GET` | `/api/assignments/instructor/{instructorId}` | List instructor assignments |
| `POST` | `/api/assignments/bulk` | Create multiple assignments |

### 📊 Grades (7 endpoints)

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/grades` | Create new grade |
| `GET` | `/api/grades/{id}` | Get grade details |
| `PUT` | `/api/grades/{id}` | Update grade |
| `DELETE` | `/api/grades/{id}` | Delete grade |
| `GET` | `/api/grades/student/{studentId}` | List student grades |
| `GET` | `/api/grades/assignment/{assignmentId}` | List assignment grades |
| `POST` | `/api/grades/bulk` | Create multiple grades |

### 📎 Attachments (7 endpoints)

**Assignment Based:**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/assignments/{assignmentId}/attachments` | Add attachment file |
| `GET` | `/api/assignments/{assignmentId}/attachments` | List assignment attachments |
| `DELETE` | `/api/assignments/{assignmentId}/attachments` | Bulk delete assignment attachments |

**File Based:**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/attachments/{id}` | Get attachment details |
| `PUT` | `/api/attachments/{id}` | Update attachment |
| `DELETE` | `/api/attachments/{id}` | Delete attachment |
| `GET` | `/api/attachments/file/{fileId}` | Get attachment by file ID |

### 🏥 Health (2 endpoints)

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/health` | Basic health check |
| `GET` | `/health/detailed` | Detailed health report |

## Request/Response Examples

### Create Assignment

**Endpoint:** `POST /api/assignments`

**Request Body:**
```json
{
    "name": "MVC Product Management",
    "details": "Develop a web application that performs product CRUD operations using ASP.NET Core MVC. Use Entity Framework Core and Repository Pattern.",
    "courseClassId": "csharp-programming-2025",
    "weekId": "week3",
    "order": 1,
    "assignedAt": "2024-01-15T09:00:00Z",
    "dueDate": "2024-01-25T23:59:00Z",
    "createdByUserId": "instructor001"
}
```

**Success Response (201):**
```json
"16e58b23-413d-4808-9465-a7e5df5ba77a"
```

**Validation Error Response (400):**
```json
[
    {
        "identifier": "Name",
        "errorMessage": "Assignment name is required."
    }
]
```

**Business Rule Error (400):**
```json
[
    {
        "identifier": "Name",
        "errorMessage": "An assignment with this name already exists in the course class."
    }
]
```

### Get Assignment Details

**Endpoint:** `GET /api/assignments/{id}`

**Response:**
```json
{
    "id": "16e58b23-413d-4808-9465-a7e5df5ba77a",
    "name": "MVC Product Management",
    "details": "Develop a web application that performs product CRUD operations using ASP.NET Core MVC. Use Entity Framework Core and Repository Pattern.",
    "courseClassId": "csharp-programming-2025",
    "weekId": "week3",
    "order": 1,
    "assignedAt": "2024-01-15T09:00:00Z",
    "dueDate": "2024-01-25T23:59:00Z",
    "createdByUserId": "instructor001"
}
```

### Create Multiple Assignments

**Endpoint:** `POST /api/assignments/bulk`

**Request Body:**
```json
{
    "assignments": [
        {
            "name": "Console Application - Calculator",
            "details": "Develop a calculator application that can perform 4 operations using C# Console Application. Use switch-case structure.",
            "courseClassId": "csharp-programming-2025",
            "weekId": "week1",
            "order": 1,
            "assignedAt": "2024-09-01T09:00:00Z",
            "dueDate": "2024-09-05T23:59:59Z",
            "createdByUserId": "instructor001"
        },
        {
            "name": "OOP Principles - Library System",
            "details": "Create a library management system using Encapsulation, Inheritance and Polymorphism principles.",
            "courseClassId": "csharp-programming-2025",
            "weekId": "week2",
            "order": 2,
            "assignedAt": "2024-09-08T09:00:00Z",
            "dueDate": "2024-09-15T23:59:59Z",
            "createdByUserId": "instructor001"
        }
    ]
}
```

**Success Response (201 - All operations successful):**
```json
{
    "totalSubmitted": 1,
    "successful": 1,
    "failed": 0,
    "results": [
        {
            "index": 0,
            "success": true,
            "assignmentId": "guid-here",
            "errors": []
        }
    ]
}
```

**Partial Success Response (200 - Some operations successful):**
```json
{
    "totalSubmitted": 2,
    "successful": 1,
    "failed": 1,
    "results": [
        {
            "index": 0,
            "success": true,
            "assignmentId": "guid-here",
            "errors": []
        },
        {
            "index": 1,
            "success": false,
            "assignmentId": null,
            "errors": [
                {
                    "identifier": "Name",
                    "errorMessage": "Assignment name is required."
                }
            ]
        }
    ]
}
```

### Create Grade

**Endpoint:** `POST /api/grades`

**Request Body:**
```json
{
    "studentId": "student-cs001",
    "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
    "givenByUserId": "instructor001",
    "grade": 88.5,
    "isFinal": false
}
```

**Response:**
```json
"2cfeb1ff-7d38-450c-b6ad-008e7dacbbdc"
```

### Get Grade Details

**Endpoint:** `GET /api/grades/{id}`

**Response:**
```json
{
    "id": "2cfeb1ff-7d38-450c-b6ad-008e7dacbbdc",
    "studentId": "student-cs001",
    "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
    "givenByUserId": "instructor001",
    "grade": 88.5,
    "isFinal": false,
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
}
```

### Add Attachment

**Endpoint:** `POST /api/assignments/{assignmentId}/attachments`

**Request Body:**
```json
{
    "fileId": "csharp-project-template",
    "fileName": "mvc-project-template.zip",
    "fileType": "application/zip"
}
```

**Response:**
```json
"5f2048d2-6d03-43d4-aff2-c269dfff1a59"
```

### List Course Assignments

**Endpoint:** `GET /api/assignments/course/{courseClassId}`

**Response:**
```json
[
    {
        "id": "16e58b23-413d-4808-9465-a7e5df5ba77a",
        "name": "MVC Product Management",
        "details": "Develop a web application that performs product CRUD operations using ASP.NET Core MVC. Use Entity Framework Core and Repository Pattern.",
        "courseClassId": "csharp-programming-2025",
        "weekId": "week3",
        "order": 1,
        "assignedAt": "2024-01-15T09:00:00Z",
        "dueDate": "2024-01-25T23:59:00Z",
        "createdByUserId": "instructor001"
    },
    {
        "id": "a7b2c3d4-5678-9abc-def0-123456789abc",
        "name": "Web API Development",
        "details": "Create a RESTful Web API. Write JWT Authentication, Swagger documentation and Unit Tests.",
        "courseClassId": "csharp-programming-2025",
        "weekId": "week4",
        "order": 2,
        "assignedAt": "2024-01-22T09:00:00Z",
        "dueDate": "2024-02-01T23:59:00Z",
        "createdByUserId": "instructor001"
    }
]
```

### List Student Grades

**Endpoint:** `GET /api/grades/student/{studentId}`

**Response:**
```json
[
    {
        "id": "2cfeb1ff-7d38-450c-b6ad-008e7dacbbdc",
        "studentId": "student-cs001",
        "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
        "givenByUserId": "instructor001",
        "grade": 88.5,
        "isFinal": false,
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
    },
    {
        "id": "3d4e5f6a-7890-bcde-f123-456789abcdef",
        "studentId": "student-cs001",
        "assignmentId": "a7b2c3d4-5678-9abc-def0-123456789abc",
        "givenByUserId": "instructor001",
        "grade": 92.0,
        "isFinal": true,
        "createdAt": "2024-02-01T14:30:00Z",
        "updatedAt": "2024-02-01T14:30:00Z"
    }
]
```

### List Assignment Attachments

**Endpoint:** `GET /api/assignments/{assignmentId}/attachments`

**Response:**
```json
[
    {
        "id": "5f2048d2-6d03-43d4-aff2-c269dfff1a59",
        "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
        "fileId": "csharp-project-template",
        "fileName": "mvc-project-template.zip",
        "fileType": "application/zip"
    },
    {
        "id": "7a8b9c0d-1e2f-3456-7890-abcdef123456",
        "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
        "fileId": "requirements-doc",
        "fileName": "project-requirements.pdf",
        "fileType": "application/pdf"
    }
]
```

## Business Rules

### Assignment Rules
- Assignment names must be unique within the same course class
- Due date must be after assigned date (if specified)
- Details field is required (cannot be empty)
- Order value is a byte value between 0-255
- CreatedByUserId (instructor) is required

### Grade Rules
- Student-assignment combination must be unique
- Final grades cannot be updated/deleted
- Grade value must be between 0-100
- GivenByUserId (instructor) is required

### Attachment Rules
- FileId, FileName, FileType are required
- Assignment must exist

## Error Handling

### HTTP Status Codes
- `200` - Successful operation
- `201` - Resource created
- `204` - Successful deletion
- `400` - Invalid request / Validation error
- `401` - Authentication required
- `404` - Resource not found
- `500` - Server error

### Error Response Format

**Validation Errors:**
```json
[
    {
        "identifier": "Name",
        "errorMessage": "Assignment name is required."
    }
]
```

**Business Rule Violations:**
```json
[
    {
        "identifier": "Name", 
        "errorMessage": "An assignment with this name already exists in the course class."
    }
]
```

**General Errors:**
```json
[
    "Assignment not found"
]
```

**Note:** All validation errors and business rule violations return 400 Bad Request status code.

### Validation Rules

**Assignment Validation:**
- Name: required, max 200 characters, unique within course class
- Details: required, max 2000 characters
- CourseClassId: required
- CreatedByUserId: required (Instructor ID)
- AssignedAt: required
- DueDate: optional, must be after AssignedAt if provided
- Order: byte value (0-255)
- WeekId: optional

**Grade Validation:**
- Grade: required, 0-100 range, max 2 decimal places
- StudentId: required
- AssignmentId: required, must exist
- GivenByUserId: required (Instructor ID)
- IsFinal: boolean

**Attachment Validation:**
- FileId: required
- FileName: required
- FileType: required
- AssignmentId: required, must exist

## Postman Collection

The project includes `Texan-Grade-Service.postman_collection.json` file.

### Import Instructions
1. Open Postman
2. Import → Upload Files
3. Select the JSON file

### Predefined Variables
- `baseUrl` - API base URL (http://localhost:8080)
- `assignmentId` - Test assignment ID
- `gradeId` - Test grade ID
- `studentId` - Test student ID (e.g.: student-cs001)
- `instructorId` - Test instructor ID (e.g.: instructor001)
- `attachmentId` - Test attachment ID
- `fileId` - Test file ID (e.g.: csharp-project-template)
- `courseId` - Test course ID (e.g.: csharp-programming-2025)

## Docker Setup
### Commands
```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs grade-api

# Stop services
docker-compose down

# Reset database
docker-compose down -v

# Rebuild and start
docker-compose up --build -d
```

## Health Checks

### Basic Health
```bash
curl http://localhost:8080/health
```

**Response:**
```json
{
    "status": "Healthy"
}
```

### Detailed Health
```bash
curl http://localhost:8080/health/detailed
```

**Response:**
```json
{
    "status": "Healthy",
    "checks": [
        {
            "name": "AppDbContext",
            "status": "Healthy",
            "description": "PostgreSQL database connection is healthy",
            "duration": 13.3795
        }
    ]
}
```