﻿using MediatR;
using System.Text.Json;
using Texan.LoggingBroker.Application.Services.Loki;
using Texan.LoggingBroker.Application.Services.Validations;
using Texan.LoggingBroker.Domain.Models;

namespace Texan.LoggingBroker.Application.Operations.GetLog;

public record GetLogsQuery(LogQueryParameters QueryParams) : IRequest<JsonElement>;

// Handler
public class GetLogsQueryHandler(ILogService lokiService,
                           IValidationDispatcher validation) : IRequestHandler<GetLogsQuery, JsonElement>
{
    public async Task<JsonElement> Handle(GetLogsQuery request, CancellationToken cancellationToken)
    {
        // 1) Validasyon
        await validation.ValidateAsync(request.QueryParams, cancellationToken);

        // 2) BusinessRules

        // 3) Call Loki service
        var logs = await lokiService.GetLogsAsync(request.QueryParams, cancellationToken);
        return logs;
    }
}