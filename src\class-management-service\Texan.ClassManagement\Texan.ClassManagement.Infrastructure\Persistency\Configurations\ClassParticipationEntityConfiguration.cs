﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Infrastructure.Persistency.Configurations
{
    internal class ClassParticipationEntityConfiguration : IEntityTypeConfiguration<ClassParticipationEntity>
    {
        public void Configure(EntityTypeBuilder<ClassParticipationEntity> builder)
        {
            BaseEntityConfiguration.ConfigureBase(builder);
            builder.Property(c => c.OperationDate).IsRequired();
            builder.Property(c => c.ClassParticipationType).IsRequired();
            builder.Property(c => c.ClassOperationType).IsRequired();
            builder.Property(c => c.UserId).IsRequired();
            builder.Property(c => c.CourseClassId).IsRequired();

            builder.HasOne(c => c.CourseClass)
                .WithMany(cc => cc.ClassParticipations)
                .HasForeignKey(c => c.CourseClassId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}