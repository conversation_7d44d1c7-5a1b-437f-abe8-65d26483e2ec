namespace Texan.Grade.Application.Operations.Grade.Commands;

public class DeleteGradeCommand
{
    public class Request : IRequest<Result<bool>>
    {
        public Guid Id { get; set; }
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Id)
                .NotEmpty().WithMessage("Grade ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<bool>>
    {
        public async Task<Result<bool>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Deleting grade: {GradeId}", request.Id);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Grade deletion failed validation: {GradeId}", request.Id);
                return validationError;
            }

            var grade = await crudService
                .Query<GradeEntity>()
                .FirstOrDefaultAsync(g => g.Id == request.Id, cancellationToken);

            if (grade is null)
            {
                logger.LogWarning("Grade not found for deletion: {GradeId}", request.Id);
                return Result.NotFound("Grade not found.");
            }

            if (grade.IsFinal)
            {
                logger.LogWarning("Cannot delete finalized grade: {GradeId}", request.Id);
                var error = new ValidationError
                {
                    Identifier = nameof(request.Id),
                    ErrorMessage = "Cannot delete a finalized grade."
                };
                return Result.Invalid(error);
            }

            await crudService.DeleteAsync(grade, cancellationToken);
        
            logger.LogInformation("Grade deleted: {GradeId}", request.Id);
            return Result.Success(true);
        }
    }
}