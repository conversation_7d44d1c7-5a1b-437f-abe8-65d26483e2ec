FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src


COPY ["Texan.Notification/Texan.Notification.Presentation.WebApi/Texan.Notification.Presentation.WebApi.csproj", "Texan.Notification.Presentation.WebApi/"]
COPY ["Texan.Notification/Texan.Notification.Infrastructure/Texan.Notification.Infrastructure.csproj", "Texan.Notification.Infrastructure/"]
COPY ["Texan.Notification/Texan.Notification.Application/Texan.Notification.Application.csproj", "Texan.Notification.Application/"]
COPY ["Texan.Notification/Texan.Notification.Domain/Texan.Notification.Domain.csproj", "Texan.Notification.Domain/"]


RUN dotnet restore "Texan.Notification.Presentation.WebApi/Texan.Notification.Presentation.WebApi.csproj"


COPY . .
WORKDIR "/src/Texan.Notification/Texan.Notification.Presentation.WebApi"
RUN dotnet publish -c Release -o /app/publish


FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app
COPY --from=build /app/publish .
ENV DOTNET_EnableDiagnostics=0
ENTRYPOINT ["dotnet", "Texan.Notification.Presentation.WebApi.dll"]