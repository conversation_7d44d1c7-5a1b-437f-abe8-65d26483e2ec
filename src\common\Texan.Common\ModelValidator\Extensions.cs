﻿using Ardalis.Result;

namespace Texan.Common.ModelValidator;

public static class Extensions
{
    public static Result ToArdalisResult(this ValidationResult result)
    {
        if (result.IsValid)
        {
            return Result.Success();
        }
        return Result.Invalid(result.Errors.SelectMany(e => e.Value, (e, v) => new ValidationError(e.Key, v)));
    }

    public static ValidationResult ToValidationResult(
        this FluentValidation.Results.ValidationResult validationResult)
    {
        if (validationResult.IsValid)
        {
            return ValidationResult.Valid();
        }
        var errors = validationResult.Errors
            .GroupBy(e => e.PropertyName, e => e.ErrorMessage)
            .ToDictionary(g => g.<PERSON>, g => g.<PERSON>(), StringComparer.OrdinalIgnoreCase);
        return ValidationResult.Invalid(errors);
    }
}