﻿using LoggingBroker.Client.BackgorundServices;
using LoggingBroker.Client.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;

namespace LoggingBroker.Client.UnitTests;

public class LogWorkerTests
{
    [Fact]
    // StopAsync çağrıldığında kuyrukta kalan loglar gönderilmeli
    public async Task StopAsync_ShouldFlushRemainingLogs()
    {
        // Arrange
        var queue = new LogQueueService(10);
        var mockClient = new Mock<ILogClient>();
        var options = Options.Create(new LoggingBrokerOptions { BatchSize = 10, SendIntervalSeconds = 60 });
        var loggerMock = new Mock<ILogger<LogWorker>>();
        var worker = new LogWorker(queue, mockClient.Object, options, loggerMock.Object);

        // 2 log ekle (ama timer beklenmeden StopAsync çağrılacak)
        queue.TryEnqueue(new LogRequestToApi { Source = "a", LogLevel = LogLevel.Information, Message = "1", EventUnixTimeMs = 1 });
        queue.TryEnqueue(new LogRequestToApi { Source = "b", LogLevel = LogLevel.Information, Message = "2", EventUnixTimeMs = 2 });

        // Act
        await worker.StopAsync(new CancellationTokenSource(500).Token);

        // Assert: StopAsync sırasında tüm loglar gönderilmiş olmalı
        mockClient.Verify(m => m.SendBatchAsync(It.Is<IEnumerable<LogRequestToApi>>(batch => batch.Count() == 2), It.IsAny<CancellationToken>()), Times.Once());
    }
}