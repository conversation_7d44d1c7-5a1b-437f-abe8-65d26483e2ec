﻿using Ardalis.Result;
using FluentValidation;
using FluentValidation.Results;
using MockQueryable.Moq;
using Moq;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Application.Operations.Assignments;
using Texan.ClassManagement.Domain.Entities;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Tests.UnitTests.Application.Assignments
{
    public class AssignStudentToClassTests
    {
        private readonly Mock<ICrudService> _mockCrudService;
        private readonly Mock<IValidator<AssignStudentToClass.Request>> _mockValidator;
        private readonly AssignStudentToClass.Handler _handler;

        public AssignStudentToClassTests()
        {
            _mockCrudService = new Mock<ICrudService>();
            _mockValidator = new Mock<IValidator<AssignStudentToClass.Request>>();
            _handler = new AssignStudentToClass.Handler(_mockCrudService.Object, _mockValidator.Object);
        }
        [Fact]
        public async Task Handle_ValidRequest_ReturnsSuccess()
        {
            // Arrange
            var request = new AssignStudentToClass.Request
            {
                CourseClassId = "class1",
                StudentId = "student1"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };
            var user = new UserEntity { Id = "student1" };
            var mockParticipationQueryable = new List<ClassParticipationEntity>()
                .AsQueryable()
                .BuildMockDbSet();
            var fakeList = new List<StudentEntity>
            {
                new StudentEntity
                {
                    Id = "student1",
                    User = user
                }
            };
            var mockStudentQueryable = fakeList.AsQueryable().BuildMockDbSet();
            string generatedId = "new-participation-id";

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignStudentToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.StudentId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(user);

            _mockCrudService.Setup(x => x.GetAll<StudentEntity>())
                .Returns(mockStudentQueryable.Object);

            _mockCrudService.Setup(x => x.GetAll<ClassParticipationEntity>())
                .Returns(mockParticipationQueryable.Object);

            _mockCrudService.Setup(x => x.AddAsync(
                It.IsAny<ClassParticipationEntity>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync((ClassParticipationEntity p, CancellationToken _) =>
                {
                    p.Id = "new-participation-id";
                    return p;
                });

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(generatedId, result.Value);
        }

        [Fact]
        public async Task Handle_InvalidRequest_ReturnsValidationError()
        {
            // Arrange
            var request = new AssignStudentToClass.Request
            {
                CourseClassId = "",
                StudentId = ""
            };

            var validationFailures = new List<ValidationFailure>
            {
                new ValidationFailure("CourseClassId", "CourseClassId is required."),
                new ValidationFailure("StudentUserId", "StudentUserId is required.")
            };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignStudentToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(2, result.ValidationErrors.Count());
        }

        [Fact]
        public async Task Handle_ClassNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new AssignStudentToClass.Request
            {
                CourseClassId = "nonexistent",
                StudentId = "student1"
            };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignStudentToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((CourseClassEntity)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Class not found.", result.Errors.First());
        }

        [Fact]
        public async Task Handle_StudentUserNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new AssignStudentToClass.Request
            {
                CourseClassId = "class1",
                StudentId = "nonexistent"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignStudentToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.StudentId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((UserEntity)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Student user not found.", result.Errors.First());
        }

        [Fact]
        public async Task Handle_StudentNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new AssignStudentToClass.Request
            {
                CourseClassId = "class1",
                StudentId = "student1"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };
            var user = new UserEntity { Id = "student1" };
            var mockStudentQueryable = new List<StudentEntity>()
                .AsQueryable()
                .BuildMockDbSet();

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignStudentToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.StudentId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(user);

            _mockCrudService.Setup(x => x.GetAll<StudentEntity>())
                .Returns(mockStudentQueryable.Object);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Student not found.", result.Errors.First());
        }

        [Fact]
        public async Task Handle_AlreadyAssigned_ReturnsConflict()
        {
            // Arrange
            var request = new AssignStudentToClass.Request
            {
                CourseClassId = "class1",
                StudentId = "student1"
            };

            var courseClass = new CourseClassEntity { Id = "class1" };
            var user = new UserEntity { Id = "student1" };
            var fakeStudentList = new List<StudentEntity>
            {
                new StudentEntity
                {
                    Id = "student1",
                    User = user
                }
            };

            var mockStudentQueryable = fakeStudentList
                .AsQueryable()
                .BuildMockDbSet();

            var fakeParticipationList = new List<ClassParticipationEntity>
            {
                new ClassParticipationEntity
                {
                    CourseClass = courseClass,
                    User = user,
                    ClassParticipationType = ClassParticipationType.Student
                }
            };

            var mockParticipationQueryable = fakeParticipationList
                .AsQueryable()
                .BuildMockDbSet();

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<AssignStudentToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(courseClass);

            _mockCrudService.Setup(x => x.GetByIdAsync<UserEntity>(
                request.StudentId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(user);

            _mockCrudService.Setup(x => x.GetAll<StudentEntity>())
                .Returns(mockStudentQueryable.Object);

            _mockCrudService.Setup(x => x.GetAll<ClassParticipationEntity>())
                .Returns(mockParticipationQueryable.Object);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(ResultStatus.Conflict, result.Status);
        }

    }
}
