﻿using Ardalis.Result;
using FluentValidation;
using SMediator.Core.Abstractions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Application.Operations.CourseClass.Queries
{
    public class GetCourseClassByIdQuery
    {
        public class Request : IRequest<Result<CourseClassEntity>>
        {
            public string Id { get; set; } = null!;
        }

        public class RequestValidator : AbstractValidator<Request>
        {
            public RequestValidator()
            {
                RuleFor(r => r.Id)
                    .NotEmpty().WithMessage("Id is required.");
            }
        }

        public class Handler(ICrudService crudService, IValidator<Request> validator) : IRequestHandler<Request, Result<CourseClassEntity>>
        {
            public async Task<Result<CourseClassEntity>> Handle(Request request, CancellationToken cancellationToken)
            {
                var validationResult = await validator.ValidateAsync(request, cancellationToken);
                if (!validationResult.IsValid)
                {
                    var errors = validationResult.Errors
                        .Select(x => new ValidationError
                        {
                            Identifier = x.PropertyName,
                            ErrorMessage = x.ErrorMessage,
                        })
                        .ToList();

                    return Result.Invalid(errors);
                }

                var courseClass = await crudService.GetByIdAsync<CourseClassEntity>(request.Id, cancellationToken);
                if (courseClass is null)
                {
                    return Result.NotFound("CourseClass not found.");
                }

                return Result.Success(courseClass);
            }
        }
    }
}
