﻿using FluentValidation;
using Microsoft.Extensions.Logging;
using Texan.LoggingBroker.Domain.Models;

namespace Texan.LoggingBroker.Application.Operations.SendLog;

public class LogRequestValidator : AbstractValidator<LogRequest>
{
    public LogRequestValidator()
    {
        RuleFor(x => x.Source).NotEmpty();
        RuleFor(x => x.Message).NotEmpty();

        RuleFor(x => x.LogLevel)
            .Must(level => Enum.IsDefined(typeof(LogLevel), level))
            .WithMessage("Invalid LogLevel value.");

        RuleFor(x => x.EventUnixTimeMs)
            .LessThanOrEqualTo(DateTimeOffset.UtcNow.ToUnixTimeMilliseconds())
            .WithMessage("Event time must be less than or equal to current time.");
    }
}