﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Texan.Common.Authentication.ApiKey;

public static class DependencyConfiguration
{
    public static IServiceCollection AddApiKeyConfiguration<TApiKeyProvider>(this IServiceCollection services,
        params string[] policyNames)
        where TApiKeyProvider : class, IApiKeyProvider
    {
        // 1) register your IApiKeyProvider implementation
        //    (in each API you wire this up to whatever you like; e.g. reading from config,
        //     database, Azure Key Vault, etc.)
        services.TryAddSingleton<IApiKeyProvider, TApiKeyProvider>();

        // 2) we’re going to add multiple named schemes
        //    so start authentication without setting a default:
        var authBuilder = services.AddAuthentication();

        foreach (var policy in policyNames)
        {
            // scheme name == policy name
            authBuilder.AddScheme<AuthenticationSchemeOptions, ApiKeyAuthenticationHandler>(
              policy, options => { /* no per-scheme options needed */ }
            );
        }

        // 3) add authorization policies that each point at its own scheme
        services.AddAuthorization(options =>
        {
            foreach (var policy in policyNames)
            {
                options.AddPolicy(policy, policyBuilder =>
                {
                    // require this specific scheme
                    policyBuilder
                      .AddAuthenticationSchemes(policy)
                      .RequireAuthenticatedUser();
                });
            }
        });

        return services;
    }

    public static void AddApiKeySwagger(this SwaggerGenOptions options)
    {
        options.AddSecurityDefinition("ApiKey", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
        {
            Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
            In = Microsoft.OpenApi.Models.ParameterLocation.Header,
            Name = ApiKeyConstants.RequestHeader,
            Description = "API Key required to access this API",
            Scheme = "scheme"
        });

        options.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
            {
                {
                    new Microsoft.OpenApi.Models.OpenApiSecurityScheme
                    {
                        Reference = new Microsoft.OpenApi.Models.OpenApiReference
                        {
                            Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                            Id = "ApiKey"
                        },
                        Scheme = "scheme",
                        Name = "ApiKey",
                        In = Microsoft.OpenApi.Models.ParameterLocation.Header
                    },
                    new List<string>()
                }
            });
    }
}