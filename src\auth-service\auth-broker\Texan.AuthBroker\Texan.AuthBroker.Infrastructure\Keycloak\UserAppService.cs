﻿using Ardalis.Result;
using Texan.AuthBroker.Application.DTOs;
using Texan.AuthBroker.Application.Services;

namespace Texan.AuthBroker.Infrastructure.Keycloak;

public class UserAppService : IUserAppService
{
    private readonly IKeycloakAdminClient _keycloak;

    public UserAppService(IKeycloakAdminClient keycloak)
    {
        _keycloak = keycloak;
    }

    public Task<Result<string>> CreateUserAsync(CreateUserRequest request)
        => _keycloak.CreateUserAsync(request);

    public Task<Result> CreateRoleAsync(string roleName)
        => _keycloak.CreateRoleAsync(roleName);

    public Task<Result> AssignRoleToUserAsync(string userId, string roleName)
        => _keycloak.AssignRoleToUserAsync(userId, roleName);

    public Task<Result> SetActiveAsync(string userId, bool isActive)
        => _keycloak.SetUserActiveAsync(userId, isActive);

    public Task<Result> ResetPasswordAsync(string userId, string newPassword)
        => _keycloak.ResetUserPasswordAsync(userId, newPassword);

    public Task<Result> CreateCustomClaimAsync(string claimName)
        => _keycloak.CreateCustomClaimAsync(claimName);

    public Task<Result> AssignCustomClaimAsync(string userId, CustomClaimRequest request)
        => _keycloak.AssignCustomClaimToUserAsync(userId, request);

    public Task<Result<TokenResponse>> LoginAsync(TokenRequest request, CancellationToken cancellationToken = default)
        => _keycloak.LoginAsync(request, cancellationToken);
}