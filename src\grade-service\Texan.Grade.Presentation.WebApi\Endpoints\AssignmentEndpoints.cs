namespace Texan.Grade.Presentation.WebApi.Endpoints;

/// <summary>
/// Assignment API endpoints
/// </summary>
public static class AssignmentEndpoints
{
    public static void MapAssignmentEndpoints(this IEndpointRouteBuilder app, bool skipAuth = false)
    {
        var group = app.MapGroup("/api/assignments")
            .WithTags("Assignments");
        
        if (!skipAuth)
        {
            group.RequireAuthorization();
        }

        // POST /api/assignments
        group.MapPost("/", async ([FromBody] CreateAssignmentCommand.Request request, IMediator mediator, CancellationToken cancellationToken) =>
        {
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.Created($"/api/assignments/{result.Value}", result.Value);
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
            
            if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                return Results.BadRequest(result.ValidationErrors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("CreateAssignment")
        .WithSummary("Create a new assignment")
        .WithDescription("Creates a new assignment for a course class")
        .Accepts<CreateAssignmentCommand.Request>("application/json")
        .Produces<string>(StatusCodes.Status201Created)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // GET /api/assignments/{id}
        group.MapGet("/{id:guid}", async (Guid id, IMediator mediator, CancellationToken cancellationToken) =>
        {
            var request = new GetAssignmentByIdQuery.Request { Id = id };
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.Ok(result.Value);
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("GetAssignmentById")
        .WithSummary("Get assignment by ID")
        .WithDescription("Retrieves a specific assignment by its identifier")
        .Produces<AssignmentEntity>()
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // PUT /api/assignments/{id}
        group.MapPut("/{id:guid}", async (Guid id, [FromBody] UpdateAssignmentCommand.Request request, IMediator mediator, CancellationToken cancellationToken) =>
        {
            request.Id = id; 
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.Ok(result.Value);
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
            
            if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                return Results.BadRequest(result.ValidationErrors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("UpdateAssignment")
        .WithSummary("Update an assignment")
        .WithDescription("Updates an existing assignment")
        .Accepts<UpdateAssignmentCommand.Request>("application/json")
        .Produces<AssignmentEntity>()
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // DELETE /api/assignments/{id}
        group.MapDelete("/{id:guid}", async (Guid id, IMediator mediator, CancellationToken cancellationToken) =>
        {
            var request = new DeleteAssignmentCommand.Request { Id = id };
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.NoContent();
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
            
            if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                return Results.BadRequest(result.ValidationErrors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("DeleteAssignment")
        .WithSummary("Delete an assignment")
        .WithDescription("Deletes an assignment if no grades or attachments exist")
        .Produces(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // GET /api/assignments/course/{courseClassId}
        group.MapGet("/course/{courseClassId}", async (string courseClassId, IMediator mediator, CancellationToken cancellationToken) =>
            {
                var request = new GetAssignmentsByCourseQuery.Request { CourseClassId = courseClassId };
                var result = await mediator.Send(request, cancellationToken);
    
                if (result.IsSuccess)
                    return Results.Ok(result.Value);
    
                if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                    return Results.BadRequest(result.ValidationErrors);
        
                return Results.BadRequest(result.Errors);
            })
            .WithName("GetAssignmentsByCourse")
            .WithSummary("Get assignments by course class")
            .WithDescription("Retrieves all assignments for a specific course class, ordered by assignment order. Returns empty array if course has no assignments.")
            .Produces<List<AssignmentEntity>>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized);

        // GET /api/assignments/week/{weekId}
        group.MapGet("/week/{weekId}", async (string weekId, IMediator mediator, CancellationToken cancellationToken) =>
            {
                var request = new GetAssignmentsByWeekQuery.Request { WeekId = weekId };
                var result = await mediator.Send(request, cancellationToken);
    
                if (result.IsSuccess)
                    return Results.Ok(result.Value);
    
                if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                    return Results.BadRequest(result.ValidationErrors);
        
                return Results.BadRequest(result.Errors);
            })
            .WithName("GetAssignmentsByWeek")
            .WithSummary("Get assignments by week")
            .WithDescription("Retrieves all assignments for a specific week. Returns empty array if week has no assignments.")
            .Produces<List<AssignmentEntity>>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized);
        
        // GET /api/assignments/instructor/{instructorId}
        group.MapGet("/instructor/{instructorId}", async (string instructorId, IMediator mediator, CancellationToken cancellationToken) =>
            {
                var request = new GetAssignmentsByInstructorQuery.Request { CreatedByUserId = instructorId };
                var result = await mediator.Send(request, cancellationToken);
    
                if (result.IsSuccess)
                    return Results.Ok(result.Value);
    
                if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                    return Results.BadRequest(result.ValidationErrors);
        
                return Results.BadRequest(result.Errors);
            })
            .WithName("GetAssignmentsByInstructor")
            .WithSummary("Get assignments by instructor")
            .WithDescription("Retrieves all assignments created by a specific instructor. Returns empty array if instructor has no assignments.")
            .Produces<List<AssignmentEntity>>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized);
        
        group.MapPost("/bulk", async ([FromBody] CreateMultipleAssignmentsCommand.Request request, IMediator mediator, CancellationToken cancellationToken) =>
            {
                var result = await mediator.Send(request, cancellationToken);
    
                if (result.IsSuccess)
                {
                    var bulkResult = result.Value;
                    if (bulkResult.Failed == 0)
                    {
                        return Results.Created("/api/assignments/bulk", bulkResult);
                    }
                    if (bulkResult.Successful > 0)
                    {
                        return Results.Ok(bulkResult);
                    }
                    return Results.BadRequest(bulkResult);
                }
                if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                    return Results.BadRequest(result.ValidationErrors);
        
                return Results.BadRequest(result.Errors);
            })
            .WithName("CreateMultipleAssignments")
            .WithSummary("Create multiple assignments")
            .WithDescription("Creates multiple assignments in a single operation. Supports partial success with detailed results.")
            .Accepts<CreateMultipleAssignmentsCommand.Request>("application/json")
            .Produces<CreateMultipleAssignmentsCommand.BulkCreateResult>(StatusCodes.Status201Created)
            .Produces<CreateMultipleAssignmentsCommand.BulkCreateResult>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized);
    }
}