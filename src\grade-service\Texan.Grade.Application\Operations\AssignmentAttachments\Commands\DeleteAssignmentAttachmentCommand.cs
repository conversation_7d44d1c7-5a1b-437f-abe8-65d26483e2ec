namespace Texan.Grade.Application.Operations.AssignmentAttachments.Commands;

public class DeleteAttachmentCommand
{
    public class Request : IRequest<Result<bool>>
    {
        public Guid Id { get; set; }
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Id)
                .NotEmpty().WithMessage("Attachment ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<bool>>
    {
        public async Task<Result<bool>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Deleting attachment: {AttachmentId}", request.Id);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
                return validationError;

            var attachment = await crudService
                .Query<AssignmentAttachmentEntity>()
                .FirstOrDefaultAsync(aa => aa.Id == request.Id, cancellationToken);

            if (attachment is null)
            {
                return Result.NotFound("Attachment not found.");
            }

            await crudService.DeleteAsync(attachment, cancellationToken);
        
            logger.LogInformation("Attachment deleted: {AttachmentId}", request.Id);
            return Result.Success(true);
        }
    }
}