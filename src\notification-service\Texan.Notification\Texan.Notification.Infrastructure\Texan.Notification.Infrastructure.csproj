﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MailKit" Version="4.12.1" />
    <PackageReference Include="FirebaseAdmin" Version="2.3.0" />
    <PackageReference Include="Google.Apis.Auth" Version="1.61.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.6" />
    <PackageReference Include="MimeKit" Version="4.12.0" />
    <PackageReference Include="Twilio" Version="7.11.3" />
	  <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Texan.Notification.Application\Texan.Notification.Application.csproj" />
    <ProjectReference Include="..\Texan.Notification.Domain\Texan.Notification.Domain.csproj" />
  </ItemGroup>

</Project>
