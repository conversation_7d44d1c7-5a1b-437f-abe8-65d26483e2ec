using Microsoft.EntityFrameworkCore.Storage;
using Texan.Grade.Application.Abstractions;
using Texan.Grade.Infrastructure.Data;

namespace Texan.Grade.Infrastructure.Persistency;

/// <summary>
/// Generic CRUD service implementation with query capabilities
/// </summary>
public class CrudService : ICrudService
{
    private readonly AppDbContext _context;

    public CrudService(AppDbContext context)
    {
        _context = context;
    }

    public IQueryable<T> GetAll<T>() where T : class
    {
        return _context.Set<T>();
    }

    public IQueryable<T> Query<T>() where T : class
    {
        return _context.Set<T>().AsQueryable();
    }

    public async Task<T?> GetByIdAsync<T>(Guid id, CancellationToken cancellationToken = default) where T : class
    {
        return await _context.Set<T>().FindAsync(id, cancellationToken);
    }

    public async Task<T> AddAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class
    {
        _context.Set<T>().Add(entity);
        await _context.SaveChangesAsync(cancellationToken);
        return entity;
    }

    public async Task<T[]> AddMultipleAsync<T>(T[] entities, CancellationToken cancellationToken = default) where T : class
    {
        _context.Set<T>().AddRange(entities);
        await _context.SaveChangesAsync(cancellationToken);
        return entities;
    }

    public async Task<T> UpdateAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class
    {
        _context.Set<T>().Update(entity);
        await _context.SaveChangesAsync(cancellationToken);
        return entity;
    }

    public async Task<T[]> UpdateMultipleAsync<T>(T[] entities, CancellationToken cancellationToken = default) where T : class
    {
        _context.Set<T>().UpdateRange(entities);
        await _context.SaveChangesAsync(cancellationToken);
        return entities;
    }

    public async Task DeleteAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class
    {
        _context.Set<T>().Remove(entity);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task DeleteMultipleAsync<T>(T[] entities, CancellationToken cancellationToken = default) where T : class
    {
        _context.Set<T>().RemoveRange(entities);
        await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task<IDbContextTransaction> GetTransactionAsync(CancellationToken cancellationToken = default)
    {
        return await _context.Database.BeginTransactionAsync(cancellationToken);
    }
}