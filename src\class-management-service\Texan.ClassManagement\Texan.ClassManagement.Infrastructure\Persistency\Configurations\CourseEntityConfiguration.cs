﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Infrastructure.Persistency.Configurations
{
    internal class CourseEntityConfiguration : IEntityTypeConfiguration<CourseEntity>
    {
        public void Configure(EntityTypeBuilder<CourseEntity> builder)
        {
            BaseEntityConfiguration.ConfigureBase(builder);
            builder.HasIndex(x => x.Name).IsUnique();
            builder.Property(x => x.Name).IsRequired().HasMaxLength(100);
            builder.HasIndex(x => x.CodePrefix).IsUnique();
            builder.Property(x => x.CodePrefix).IsRequired().HasMaxLength(10);
            builder.Property(x => x.Level).IsRequired();
            builder.Property(x => x.DurationWeek).IsRequired();
        }
    }
}
