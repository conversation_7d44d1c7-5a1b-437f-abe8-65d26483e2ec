﻿using System.ComponentModel.DataAnnotations.Schema;
using Texan.ClassManagement.Domain.Enums;
using Texan.ClassManagement.Domain.ValueObjects;

namespace Texan.ClassManagement.Domain.Entities
{
    // TP199, BE126 vs.
    public class CourseClassEntity : BaseEntity
    {
        public string Name { get; set; } = null!;
        public ClassType ClassType { get; set; }

        // TODO: not mapped props should be checked for correctness
        [NotMapped]
        public DateTime? StartTime => Weeks.FirstOrDefault()?
            .DaySchedules.FirstOrDefault()?
            .StartDateTime;

        [NotMapped]
        public DateTime? EndTime => Weeks.LastOrDefault()?
            .DaySchedules.LastOrDefault()?
            .EndDateTime;

        [NotMapped]
        public int? TotalWeekCount => Weeks.Count;

        [NotMapped]
        public int? CourseWeekCount => Weeks
            .Where(x => !x.IsBreakWeek).ToList().Count;

        [NotMapped]
        public int? BreakWeekCount => Weeks
            .Where(x => x.IsBreakWeek).ToList().Count;

        // Foreign Key
        public string CourseId { get; set; } = null!;

        // Navigation
        public CourseEntity Course { get; set; } = null!;

        public ICollection<ClassParticipationEntity> ClassParticipations { get; set; } = [];
        public ICollection<Calendar.WeekSchedule> Weeks { get; set; } = [];
    }
}