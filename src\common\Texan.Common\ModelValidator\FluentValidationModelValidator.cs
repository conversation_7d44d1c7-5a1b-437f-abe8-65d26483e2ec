﻿using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Texan.Common.ModelValidator;

internal class FluentValidationModelValidator(IServiceProvider serviceProvider, ILogger<FluentValidationModelValidator> logger) : IModelValidator
{
    private readonly IServiceProvider _serviceProvider = serviceProvider;
    private readonly ILogger<FluentValidationModelValidator> _logger = logger;

    public async Task<ValidationResult> ValidateAsync<T>(T model, CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var validator = scope.ServiceProvider.GetService<IValidator<T>>();
        if (validator is null)
        {
            _logger.LogWarning("No validator found for type {Type}", typeof(T).Name);
            return ValidationResult.Valid();
        }
        var result = await validator.ValidateAsync(model, cancellationToken);

        return result.ToValidationResult();
    }
}