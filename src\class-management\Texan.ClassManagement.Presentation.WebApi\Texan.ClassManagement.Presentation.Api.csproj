﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>4bbdc72d-b64a-4447-8302-91d49f8821e7</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <DockerComposeProjectPath>..\..\..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.Result" Version="10.1.0" />
    <PackageReference Include="Ardalis.Result.AspNetCore" Version="10.1.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Texan.ClassManagement.Application\Texan.ClassManagement.Application.csproj" />
    <ProjectReference Include="..\Texan.ClassManagement.Infrastructure\Texan.ClassManagement.Infrastructure.csproj" />
  </ItemGroup>

</Project>
