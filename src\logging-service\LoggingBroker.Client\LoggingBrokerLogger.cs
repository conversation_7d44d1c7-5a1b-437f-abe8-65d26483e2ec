﻿using LoggingBroker.Client.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;

namespace LoggingBroker.Client;

public class LoggingBrokerLogger(string category,
                            LogQueueService queue,
                            LoggingBrokerOptions options) : ILogger
{
    private readonly LogLevel _minLevel = options.MinimumLevel;

    public IDisposable BeginScope<TState>(TState state) where TState : notnull
        => NullScope.Instance;

    public bool IsEnabled(LogLevel logLevel)
        => logLevel != LogLevel.None && logLevel >= _minLevel;

    public void Log<TState>(LogLevel logLevel,
                             EventId eventId,
                             TState state,
                             Exception? exception,
                             Func<TState, Exception?, string> formatter)
    {
        if (!IsEnabled(logLevel)) return;

        var message = formatter(state, exception);
        Dictionary<string, string>? parameters = null;

        if (state is IEnumerable<KeyValuePair<string, object>> props)
        {
            parameters = props
                .ToDictionary(k => k.Key, k => k.Value?.ToString() ?? string.Empty);
        }

        var log = new LogRequestToApi
        {
            Source = category,
            LogLevel = logLevel,
            Message = message,
            Parameters = parameters,
            EventUnixTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };

        queue.TryEnqueue(log);
    }
}