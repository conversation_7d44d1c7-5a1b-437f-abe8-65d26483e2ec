﻿using System.Net;
using System.Text.Json;
using FluentValidation;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Texan.Notification.Presentation.WebApi.WebApiExtensions.Middlewares;

public class ExceptionHandlingMiddleware : IMiddleware
{
    private readonly ILogger<ExceptionHandlingMiddleware> _logger;

    public ExceptionHandlingMiddleware(ILogger<ExceptionHandlingMiddleware> logger)
    {
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            var statusCode = GetStatusCode(ex);
            var errorResponse = CreateErrorResponse(ex, context, statusCode);

            _logger.LogError(ex, "[Exception] {ExceptionType} at {Path} — {Message}",
                ex.GetType().Name,
                context.Request.Path,
                ex.Message);

            context.Response.StatusCode = statusCode;
            context.Response.ContentType = "application/json";

            var json = JsonSerializer.Serialize(errorResponse);
            await context.Response.WriteAsync(json);
        }
    }

    private static int GetStatusCode(Exception ex) => ex switch
    {
        ValidationException => (int)HttpStatusCode.BadRequest,
        UnauthorizedAccessException => (int)HttpStatusCode.Unauthorized,
        KeyNotFoundException => (int)HttpStatusCode.NotFound,
        _ => (int)HttpStatusCode.InternalServerError
    };

    private static object CreateErrorResponse(Exception ex, HttpContext context, int statusCode)
    {
        return new
        {
            statusCode,
            error = ex switch
            {
                ValidationException validationEx => validationEx.Errors.Select(e => e.ErrorMessage).ToList(),
                _ => new List<string> { ex.Message }
            },
            exception = ex.GetType().Name,
            path = context.Request.Path
        };
    }
}
