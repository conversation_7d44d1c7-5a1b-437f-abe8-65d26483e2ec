﻿namespace Texan.ClassManagement.Domain.Entities
{
    public class StudentEntity : BaseEntity
    {
        public string UserId { get; set; } = null!;
        public string StudentNumber { get; set; } = null!;
        public DateTime? EnrollmentDate { get; set; }

        // Navigation
        public UserEntity User { get; set; } = null!;

        public ICollection<ClassParticipationEntity>? ClassParticipations { get; set; }
        public ICollection<CourseEntity>? Courses { get; set; }
        public ICollection<CourseClassEntity>? CourseClasses { get; set; }
        public ICollection<GradeResultEntity>? GradeResults { get; set; }
    }
}