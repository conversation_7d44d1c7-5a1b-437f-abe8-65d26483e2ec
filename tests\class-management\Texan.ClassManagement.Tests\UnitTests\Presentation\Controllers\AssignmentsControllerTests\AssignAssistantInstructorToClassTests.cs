﻿using Ardalis.Result;
using Moq;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Operations.Assignments;
using Texan.ClassManagement.Presentation.WebApi.Controllers;

namespace Texan.ClassManagement.Tests.UnitTests.Presentation.Controllers.AssignmentsControllerTests
{
    public class AssignAssistantInstructorToClassTests
    {
        private readonly Mock<IMediator> _mockMediator;
        private readonly AssignmentsController _controller;

        public AssignAssistantInstructorToClassTests()
        {
            _mockMediator = new Mock<IMediator>();
            _controller = new AssignmentsController(_mockMediator.Object);
        }

        [Fact]
        public async Task AssignAssistantInstructorToClass_ValidRequest_ReturnsSuccessResult()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "class1",
                AssistantInstructorUserId = "assistant1"
            };

            var expectedResult = Result.Success("participation1");

            _mockMediator.Setup(x => x.Send(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.AssignAssistantInstructorToClass(request, CancellationToken.None);
            var typedResult = Assert.IsAssignableFrom<Result<string>>(result);

            // Assert
            Assert.True(typedResult.IsSuccess);
            Assert.Equal(expectedResult.Value, typedResult.Value);
            _mockMediator.Verify(x => x.Send(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task AssignAssistantInstructorToClass_InvalidRequest_ReturnsValidationError()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "",
                AssistantInstructorUserId = ""
            };

            var validationError = Result.Invalid(new ValidationError { ErrorMessage = "CourseClassId is required." });

            _mockMediator.Setup(x => x.Send(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(validationError);

            // Act
            var result = await _controller.AssignAssistantInstructorToClass(request, CancellationToken.None);
            var typedResult = Assert.IsAssignableFrom<Result<string>>(result);

            // Assert
            Assert.False(typedResult.IsSuccess);
            Assert.Equal(validationError.ValidationErrors.First().ErrorMessage, typedResult.ValidationErrors.First().ErrorMessage);
        }

        [Fact]
        public async Task AssignAssistantInstructorToClass_ClassNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "class1",
                AssistantInstructorUserId = "assistant1"
            };

            var notFoundResult = Result.NotFound("Class not found");

            _mockMediator.Setup(x => x.Send(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(notFoundResult);
            // Act
            var result = await _controller.AssignAssistantInstructorToClass(request, CancellationToken.None);
            var typedResult = Assert.IsAssignableFrom<Result<string>>(result);

            // Assert
            Assert.False(typedResult.IsSuccess);
            Assert.Equal(notFoundResult.Errors.First(), typedResult.Errors.First());
        }

        [Fact]
        public async Task AssignAssistantInstructorToClass_UserNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "class1",
                AssistantInstructorUserId = "assistant1"
            };

            var notFoundResult = Result.NotFound("User not found.");

            _mockMediator.Setup(x => x.Send(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(notFoundResult);

            // Act
            var result = await _controller.AssignAssistantInstructorToClass(request, CancellationToken.None);
            var typedResult = Assert.IsAssignableFrom<Result<string>>(result);

            // Assert
            Assert.False(typedResult.IsSuccess);
            Assert.Equal(notFoundResult.Errors.First(), typedResult.Errors.First());
        }

        [Fact]
        public async Task AssignAssistantInstructorToClass_UserNotInstructor_ReturnsForbidden()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "class1",
                AssistantInstructorUserId = "assistant1"
            };

            var forbiddenResult = Result.Forbidden("User is not an Assistant Instructor.");

            _mockMediator.Setup(x => x.Send(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(forbiddenResult);

            // Act
            var result = await _controller.AssignAssistantInstructorToClass(request, CancellationToken.None);
            var typedResult = Assert.IsAssignableFrom<Result<string>>(result);

            // Assert
            Assert.False(typedResult.IsSuccess);
            Assert.Equal(forbiddenResult.Errors.First(), typedResult.Errors.First());
        }

        [Fact]
        public async Task AssignAssistantInstructorToClass_AssistantInstructorAlreadyAssigned_ReturnsConflict()
        {
            // Arrange
            var request = new AssignAssistantInstructorToClass.Request
            {
                CourseClassId = "class1",
                AssistantInstructorUserId = "assistant1"
            };

            var conflictResult = Result.Conflict("Assistant instructor already assigned to this class.");

            _mockMediator.Setup(x => x.Send(
                It.IsAny<AssignAssistantInstructorToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(conflictResult);

            // Act
            var result = await _controller.AssignAssistantInstructorToClass(request, CancellationToken.None);
            var typedResult = Assert.IsAssignableFrom<Result<string>>(result);

            // Assert
            Assert.False(typedResult.IsSuccess);
            Assert.Equal(conflictResult.Errors.First(), typedResult.Errors.First());
        }
    }
}
