﻿using Ardalis.Result;
using FluentValidation;
using FluentValidation.Results;
using MockQueryable.Moq;
using Moq;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Application.Operations.RoleCheck;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Tests.UnitTests.Application.RoleCheck
{
    public class CheckUserRoleTests
    {
        private readonly Mock<ICrudService> _mockCrudService;
        private readonly Mock<IValidator<CheckUserRole.Request>> _mockValidator;
        private readonly CheckUserRole.Handler _handler;

        public CheckUserRoleTests()
        {
            _mockCrudService = new Mock<ICrudService>();
            _mockValidator = new Mock<IValidator<CheckUserRole.Request>>();
            _handler = new CheckUserRole.Handler(_mockCrudService.Object, _mockValidator.Object);
        }

        [Fact]
        public async Task Handle_UserHasSpecifiedRole_ReturnsSuccess()
        {
            // Arrange
            var request = new CheckUserRole.Request
            {
                UserId = "user1",
                Role = "role1"
            };

            var userEntity = new UserEntity
            {
                Id = "user1",
                Roles = new List<RoleEntity>
                {
                    new RoleEntity { Name = "role1" }
                }
            };

            var users = new List<UserEntity> { userEntity }.AsQueryable();
            var mockUserSet = users.AsQueryable().BuildMockDbSet();

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<CheckUserRole.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetAll<UserEntity>())
                .Returns(mockUserSet.Object);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            //Assert
            Assert.True(result.IsSuccess);
        }

        [Fact]
        public async Task Handle_UserDoesNotHaveSpecifiedRole_ReturnsForbidden()
        {
            // Arrange
            var request = new CheckUserRole.Request
            {
                UserId = "user1",
                Role = "role1"
            };

            var userEntity = new UserEntity
            {
                Id = "user1",
                Roles = new List<RoleEntity>
                {
                    new RoleEntity { Name = "role2" }
                }
            };

            var users = new List<UserEntity> { userEntity }.AsQueryable();
            var mockUserSet = users.AsQueryable().BuildMockDbSet();

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<CheckUserRole.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(x => x.GetAll<UserEntity>())
                .Returns(mockUserSet.Object);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(ResultStatus.Forbidden, result.Status);
            Assert.Empty(result.Errors);
        }

        [Fact]
        public async Task Handle_InvalidRequest_ReturnsValidationError()
        {
            // Arrange
            var request = new CheckUserRole.Request
            {
                UserId = "",
                Role = ""
            };

            var validationFailures = new List<ValidationFailure>
            {
                new ValidationFailure("UserId", "UserId is required."),
                new ValidationFailure("Role", "Role is required.")
            };

            _mockValidator.Setup(x => x.ValidateAsync(
                It.IsAny<CheckUserRole.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(2, result.ValidationErrors.Count());
        }
    }
}
