using Texan.LoggingBroker.Application;
using Texan.LoggingBroker.Infrastructure;
using Texan.LoggingBroker.Infrastructure.Services.Loki;
using Texan.LoggingBroker.WebApi;
using Texan.LoggingBroker.WebApi.Extensions.Endpoints;
using Texan.LoggingBroker.WebApi.Extensions.Exceptions;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddHealthChecks();
builder.AddSerilogLogging();

builder.Services.AddInfrastructureServices(builder.Configuration);
builder.Services.AddApplicationServices();
builder.Services.AddWebApiServices(builder.Configuration);

var app = builder.Build();

app.UseCustomExceptionHandler();

app.UseRouting();
app.UseRateLimiter();

app.UseHealthChecks("/health");

app.MapRoutes();

app.Run();

