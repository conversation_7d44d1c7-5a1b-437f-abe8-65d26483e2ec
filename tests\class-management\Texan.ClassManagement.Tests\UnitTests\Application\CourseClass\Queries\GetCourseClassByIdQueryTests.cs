﻿using FluentValidation;
using FluentValidation.Results;
using Moq;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Application.Operations.CourseClass.Queries;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Tests.UnitTests.Application.CourseClass.Queries
{
    public class GetCourseClassByIdQueryTests
    {
        private readonly Mock<ICrudService> _mockCrudService;
        private readonly Mock<IValidator<GetCourseClassByIdQuery.Request>> _mockValidator;
        private readonly GetCourseClassByIdQuery.Handler _handler;

        public GetCourseClassByIdQueryTests()
        {
            _mockCrudService = new Mock<ICrudService>();
            _mockValidator = new Mock<IValidator<GetCourseClassByIdQuery.Request>>();
            _handler = new GetCourseClassByIdQuery.Handler(_mockCrudService.Object, _mockValidator.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ReturnsSuccess()
        {
            // Arrange
            var request = new GetCourseClassByIdQuery.Request { Id = "class1" };
            var expectedEntity = new CourseClassEntity { Id = "class1", Name = "Test Class" };

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<GetCourseClassByIdQuery.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(s => s.GetByIdAsync<CourseClassEntity>(
                request.Id,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(expectedEntity);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal("class1", result.Value.Id);
        }

        [Fact]
        public async Task Handle_InvalidRequest_ReturnsValidationError()
        {
            // Arrange
            var request = new GetCourseClassByIdQuery.Request { Id = "" };
            var validationFailures = new List<ValidationFailure>
            {
                new ValidationFailure("Id", "Id is required.")
            };

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<GetCourseClassByIdQuery.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Single(result.ValidationErrors);
        }

        [Fact]
        public async Task Handle_NonExistingId_ReturnsNotFound()
        {
            // Arrange
            var request = new GetCourseClassByIdQuery.Request { Id = "nonexistent" };

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<GetCourseClassByIdQuery.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(s => s.GetByIdAsync<CourseClassEntity>(
                request.Id,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((CourseClassEntity)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("CourseClass not found.", result.Errors.First());
        }
    }
}
