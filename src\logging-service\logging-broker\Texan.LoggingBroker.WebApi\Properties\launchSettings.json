﻿{
  "$schema": "https://json.schemastore.org/launchsettings.json",
  "profiles": {
    "http": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": false,
      "applicationUrl": "http://localhost:5076",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "LokiUrl": "http://localhost:3011"
      }
    },
    "https": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": false,
      "applicationUrl": "https://localhost:7169;http://localhost:5076",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "LokiUrl": "http://localhost:3011"
      }
    },
    "docker": {
      "commandName": "Docker",
      "launchBrowser": false,
      "publishAllPorts": true, 
      "useSSL": false,
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "LokiUrl": "http://loki:3100"
      }
    }
  }
}
