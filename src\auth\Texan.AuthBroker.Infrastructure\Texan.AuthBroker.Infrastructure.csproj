﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Texan.AuthBroker.Application\Texan.AuthBroker.Application.csproj" />
    <ProjectReference Include="..\Texan.AuthBroker.Domain\Texan.AuthBroker.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
	<FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

</Project>
