﻿using FluentValidation;
using FluentValidation.Results;
using MockQueryable.Moq;
using Moq;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Application.Operations.CourseClass.Commands;
using Texan.ClassManagement.Domain.Entities;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Tests.UnitTests.Application.CourseClass.Commands
{
    public class CreateCourseClassCommandTests
    {
        private readonly Mock<ICrudService> _mockCrudService;
        private readonly Mock<IValidator<CreateCourseClassCommand.Request>> _mockValidator;
        private readonly CreateCourseClassCommand.Handler _handler;

        public CreateCourseClassCommandTests()
        {
            _mockCrudService = new Mock<ICrudService>();
            _mockValidator = new Mock<IValidator<CreateCourseClassCommand.Request>>();
            _handler = new CreateCourseClassCommand.Handler(_mockCrudService.Object, _mockValidator.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ReturnsSuccess()
        {
            // Arrange
            var request = new CreateCourseClassCommand.Request
            {
                Name = "Physics 101",
                ClassType = ClassType.Weekday,
                CourseId = "course1"
            };

            var course = new CourseEntity { Id = "course1" };
            var mockCourseClassQueryable = new List<CourseClassEntity>().AsQueryable().BuildMockDbSet();

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<CreateCourseClassCommand.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(s => s.GetByIdAsync<CourseEntity>(
                request.CourseId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(course);

            _mockCrudService.Setup(s => s.GetAll<CourseClassEntity>())
                .Returns(mockCourseClassQueryable.Object);

            _mockCrudService.Setup(s => s.AddAsync(
                It.IsAny<CourseClassEntity>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((CourseClassEntity Entity, CancellationToken _) =>
                    {
                        Entity.Id = "new-class-id";
                        return Entity;
                    });

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal("new-class-id", result.Value);
        }

        [Fact]
        public async Task Handle_InvalidRequest_ReturnsValidationErrors()
        {
            // Arrange
            var request = new CreateCourseClassCommand.Request
            {
                Name = "",
                ClassType = ClassType.Weekday,
                CourseId = ""
            };
            var validationFailures = new List<ValidationFailure>
            {
                new ValidationFailure("Name", "Class name is required."),
                new ValidationFailure("CourseId", "Id is required.")
            };

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<CreateCourseClassCommand.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(2, result.ValidationErrors.Count());
        }

        [Fact]
        public async Task Handle_CourseNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new CreateCourseClassCommand.Request
            {
                Name = "Chemistry 101",
                ClassType = ClassType.Weekend,
                CourseId = "nonexistent"
            };

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<CreateCourseClassCommand.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(s => s.GetByIdAsync<CourseEntity>(
                request.CourseId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((CourseEntity)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Course not found.", result.Errors.First());
        }

        [Fact]
        public async Task Handle_DuplicateClassName_ReturnsValidationError()
        {
            // Arrange
            var request = new CreateCourseClassCommand.Request
            {
                Name = "Math 101",
                ClassType = ClassType.Weekday,
                CourseId = "course1"
            };

            var course = new CourseEntity { Id = "course1" };
            var fakeList = new List<CourseClassEntity>
            {
                new CourseClassEntity
                {
                    Name = "Math 101",
                    CourseId = "course1"
                }
            };

            var mockCourseClassQueryable = fakeList.AsQueryable().BuildMockDbSet();

            _mockValidator.Setup(v => v.ValidateAsync(
                request,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(s => s.GetByIdAsync<CourseEntity>(
                request.CourseId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(course);

            _mockCrudService.Setup(s => s.GetAll<CourseClassEntity>())
                .Returns(mockCourseClassQueryable.Object);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            var error = result.ValidationErrors.Single();
            Assert.Equal(nameof(request.Name), error.Identifier);
            Assert.Equal("A class with this name already exists for the specified course.", error.ErrorMessage);
        }
    }
}
