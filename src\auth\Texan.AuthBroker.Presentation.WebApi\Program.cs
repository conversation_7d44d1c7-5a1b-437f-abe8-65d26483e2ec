﻿using FluentValidation;
using Texan.AuthBroker.Application;
using Texan.AuthBroker.Application.Services;
using Texan.AuthBroker.Infrastructure;
using Texan.AuthBroker.Infrastructure.HealthCheck;
using Texan.AuthBroker.Infrastructure.Keycloak;
using Texan.AuthBroker.Presentation.Api.Extensions;

var builder = WebApplication.CreateBuilder(args);

// 1) Add Application & Infrastructure services
builder.Services.AddScoped<IUserAppService, UserAppService>();
builder.Services.AddInfrastructureServices(builder.Configuration);
builder.Services.AddValidatorsFromAssemblyContaining<IApplicationAssemblyMarker>();

// ✅ Swagger servisleri
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// ✅ HealthCheck servisi
builder.Services.AddHealthChecks()
    .AddCheck<KeycloakHealthCheck>("keycloak");

// 2) Build
var app = builder.Build();

// ✅ Swagger middleware
app.UseSwagger();
app.UseSwaggerUI();

// ✅ Texan realmCreate
app.InitializeRealm();

// 3) Minimal Endpointler
app.MapUserEndpoints();

app.MapCustomHealthChecks();

await app.RunAsync();