﻿using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Domain.Entities
{
    public class Calendar
    {
        public class WeekScheduleEntity : BaseEntity
        {
            // Haftanın numarası (ör. 1. hafta, 2. hafta, ...)
            public int WeekNumber { get; set; }

            // Bu haftanın ara tatil (break week) olup olmadığı
            public bool IsBreakWeek { get; set; }

            // Haftanın her bir günü için plan (örneğin hafta içi sınıflarda Pazartesi, Salı, Çarşamba için ders bilgisi)
            public ICollection<DayScheduleEntity> DaySchedules { get; set; } = null!;
        }

        // Gün bazında planlama bilgileri
        public class DayScheduleEntity
        {
            // DayOfWeek tipi kullanılarak gün bilgisi (Pazartesi, Salı, vb.)
            public DayOfWeek Day => StartDateTime.DayOfWeek;

            // Bu günün takvimdeki tipi: <PERSON><PERSON>, ara tatil veya etüt
            public CalendarEntryType EntryType { get; set; }

            // Ders ya da etüt varsa başlama saati
            public DateTime StartDateTime { get; set; }

            // Ders ya da etüt varsa bitiş saati
            public DateTime EndDateTime { get; set; }
        }
    }
}