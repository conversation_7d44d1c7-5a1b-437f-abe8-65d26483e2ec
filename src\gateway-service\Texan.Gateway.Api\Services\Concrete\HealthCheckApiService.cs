﻿using System.Collections.Concurrent;
using Texan.Gateway.Api.Services.Abstract;

namespace Texan.Gateway.Api.Services.Concrete
{
    public class HealthCheckApiService(IConfiguration _configuration,
        ILogger<HealthCheckApiService> logger,
        IHttpClientFactory _httpClientFactory)
        : IHealthCheckApiService
    {
        public async Task<IResult> CheckHealthAsync(string sectionName, string environment)
        {
            var services = _configuration.GetSection(sectionName).Get<Dictionary<string, string>>();
            if (services is null)
                return Results.NotFound(new { error = "No services configured for health check." });

            var httpClient = _httpClientFactory.CreateClient();
            var result = new
            {
                environment,
                services = new ConcurrentDictionary<string, int>()
            };

            List<Task> taskList = [.. services.Select(service =>
            {
                return Task.Run(async () =>
                {
                    CancellationTokenSource cts = new(TimeSpan.FromSeconds(10));
                    try
                    {
                        var response = await httpClient.GetAsync(service.Value, cts.Token);
                        result.services[service.Key] = (int)response.StatusCode;
                    }
                    catch (TaskCanceledException tcex)
                    {
                        logger.LogWarning(tcex, "Health check for service {ServiceKey} timed out.", service.Key);
                        result.services.TryAdd(service.Key, 408); // 408 Request Timeout
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error checking health for service {ServiceKey}", service.Key);
                        result.services.TryAdd(service.Key, 404);
                    }
                });
            })];

            await Task.WhenAll(taskList);

            return Results.Ok(result);
        }
    }
}