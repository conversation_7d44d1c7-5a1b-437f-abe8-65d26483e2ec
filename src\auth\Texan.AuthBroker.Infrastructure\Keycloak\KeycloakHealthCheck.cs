﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Texan.AuthBroker.Infrastructure.Keycloak
{
    public class KeycloakHealthCheck : IHealthCheck
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _config;

        public KeycloakHealthCheck(IHttpClientFactory httpClientFactory, IConfiguration config)
        {
            _httpClientFactory = httpClientFactory;
            _config = config;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                var client = _httpClientFactory.CreateClient(nameof(IKeycloakAdminClient));
                var baseUrl = _config["Keycloak:BaseUrl"] ?? "http://localhost:8080/";

                var resp = await client.GetAsync($"{baseUrl}realms/master", cancellationToken);

                if (resp.IsSuccessStatusCode)
                {
                    return HealthCheckResult.Healthy("Keycloak is reachable.");
                }

                return HealthCheckResult.Unhealthy($"Keycloak returned status code {resp.StatusCode}");
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy($"Keycloak connection failed: {ex.Message}");
            }
        }
    }
}