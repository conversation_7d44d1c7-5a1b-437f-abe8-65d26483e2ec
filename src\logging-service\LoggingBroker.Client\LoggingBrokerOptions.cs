﻿using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;

namespace LoggingBroker.Client;

public class LoggingBrokerOptions
{
    [Required(ErrorMessage = "BaseUrl is required.")]
    [Url(ErrorMessage = "BaseUrl must be a valid URL.")]
    public string BaseUrl { get; set; } = default!;

    [Required(ErrorMessage = "LogEndpoint is required.")]
    public string LogEndpoint { get; set; } = "/api/v1/logs";

    [Range(1, int.MaxValue)]
    public int BatchSize { get; set; } = 100; // Http istedğinde maks kullanılacak log sayısı

    [Range(1, int.MaxValue)]
    public int SendIntervalSeconds { get; set; } = 3; // Log gönderim aralığı saniye cinsinden

    [Range(0, int.MaxValue)]
    public int MaxQueueSize { get; set; } = 10000; // kuyrukta tutulacak maksimum log sayısı
    [Range(0.01, 1.0)]
    public double CircuitBreakerFailureRate { get; set; } = 0.5; // circuit hata oranı eşiği

    [Range(1, int.MaxValue)]
    public int CircuitBreakerSamplingSeconds { get; set; } = 30; // circuit breaker örnekleme süresi
    [Range(1, int.MaxValue)]
    public int CircuitBreakerMinimumThroughput { get; set; } = 10;// circuit breaker için minimum throughput yani minimum log sayısı

    [Range(1, int.MaxValue)]
    public int CircuitBreakerBreakDurationSeconds { get; set; } = 30; // circuit breaker tetiklendikten sonra bekleme süresi
    [Range(1, int.MaxValue)]
    public int RetryBackoffBaseSeconds { get; set; } = 2; // hata sonrası bekleme süresi

    [Range(0, int.MaxValue)]
    public int MaxRetryCount { get; set; } = 5; 

    [Range(1, double.MaxValue)]
    public double HttpClientTimeoutSeconds { get; set; } = 30; 

    [EnumDataType(typeof(LogLevel))]
    public LogLevel MinimumLevel { get; set; } = LogLevel.Information;
}
