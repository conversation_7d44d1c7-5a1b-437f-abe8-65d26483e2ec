﻿using FluentAssertions;
using Microsoft.Extensions.Options;

namespace LoggingBroker.Client.UnitTests;

public class LoggingBrokerLoggerProviderTests
{
    [Fact]
    // Provider aynı kategoriye aynı instance’ı döndürüyor mu?
    public void CreateLogger_ShouldReturnSameInstance_ForSameCategory()
    {
        var options = Options.Create(new LoggingBrokerOptions());
        var queue = new LogQueueService(10);
        var provider = new LoggingBrokerLoggerProvider(options, queue);

        var logger1 = provider.CreateLogger("cat");
        var logger2 = provider.CreateLogger("cat");

        logger1.Should().BeSameAs(logger2);
    }
}