﻿using Microsoft.Extensions.Options;
using System.Threading.RateLimiting;
using Texan.LoggingBroker.WebApi.BackgroundServices;
using Texan.LoggingBroker.WebApi.Extensions;

namespace Texan.LoggingBroker.WebApi;

public static class WebApiServiceRegistrations
{
    public static IServiceCollection AddWebApiServices(
        this IServiceCollection services, IConfiguration configuration)
    {
        services.AddHostedService<LogQueueConsumerBackgroundService>();
        services.AddOptions<RateLimiterSettings>()
            .Bind(configuration.GetSection(nameof(RateLimiterSettings)))
            .ValidateDataAnnotations();

        services.AddRateLimiter(options =>
        {
            var serviceProvider = services.BuildServiceProvider();
            var limiterSettings = serviceProvider.GetRequiredService<IOptions<RateLimiterSettings>>().Value;

            options.GlobalLimiter = PartitionedRateLimiter.Create<HttpContext, string>(context =>
                RateLimitPartition.GetFixedWindowLimiter("global", _ => new FixedWindowRateLimiterOptions
                {
                    PermitLimit = limiterSettings.PermitLimit,
                    Window = TimeSpan.FromSeconds(limiterSettings.WindowTime),
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst,
                    QueueLimit = limiterSettings.QueueLimit
                }));

            options.RejectionStatusCode = StatusCodes.Status429TooManyRequests;
        });
        return services;
    }
}
