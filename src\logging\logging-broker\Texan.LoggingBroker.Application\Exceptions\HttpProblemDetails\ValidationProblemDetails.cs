﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Texan.LoggingBroker.Application.Exceptions.Types;

namespace Texan.LoggingBroker.Application.Exceptions.HttpProblemDetails;

public class ValidationProblemDetails : ProblemDetails
{
    public IEnumerable<ValidationExceptionModel> Errors { get; init; }

    public ValidationProblemDetails(IEnumerable<ValidationExceptionModel> errors)
    {
        Title = "Validation error(s)";
        Detail = "One or more validation errors occurred.";
        Errors = errors;
        Status = StatusCodes.Status422UnprocessableEntity;
    }
}
