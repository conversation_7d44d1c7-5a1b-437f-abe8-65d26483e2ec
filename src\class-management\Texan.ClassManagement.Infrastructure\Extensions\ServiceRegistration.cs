﻿using Microsoft.Extensions.DependencyInjection;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Infrastructure.Persistency;

namespace Texan.ClassManagement.Infrastructure.Extensions
{
    public static class ServiceRegistration
    {
        public static IServiceCollection AddInfrastructureServices(this IServiceCollection services)
        {
            services.AddScoped<ICrudService, CrudService>();
            return services;
        }
    }
}