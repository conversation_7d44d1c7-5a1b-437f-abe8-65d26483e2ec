﻿using Polly;
using System.Text.Json;
using Texan.LoggingBroker.Application.Services.AsyncQueue;
using Texan.LoggingBroker.Application.Services.Loki;
using Texan.LoggingBroker.Domain.Models;

namespace Texan.LoggingBroker.WebApi.BackgroundServices;

public class LogQueueConsumerBackgroundService(
    IAsyncQueueService<QueuedLogItem> queueService,
    IServiceScopeFactory serviceScopeFactory,
    ILogger<LogQueueConsumerBackgroundService> logger
) : BackgroundService
{
    private const int MaxRetryCount = 10;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        await foreach (var queuedItem in queueService.DequeueAsync(stoppingToken))
        {
            // ILogService is registered as Scoped, so we need to create a new scope
            using var scope = serviceScopeFactory.CreateScope();
            var logService = scope.ServiceProvider.GetRequiredService<ILogService>();

            try
            {
                var retryPolicy = Policy
                    .Handle<Exception>()
                    .WaitAndRetryAsync(
                        retryCount: 3,
                        sleepDurationProvider: attempt => TimeSpan.FromSeconds(Math.Pow(2, attempt)),
                        onRetry: (exception, delay, retryAttempt, _) =>
                        {
                            logger.LogWarning(exception,
                                "[{Source}] log failed to send to Loki at {RetryAttempt} attempt.",
                                queuedItem.Request.Source, retryAttempt);
                        });

                await retryPolicy.ExecuteAsync(() =>
                    logService.SendLogAsync(queuedItem.Request, stoppingToken));
            }
            catch (Exception ex)
            {
                logger.LogError(ex,
                    "[{Source}] log could not be sent to Loki after 3 retries. Re-queuing log: {@Log}",
                    queuedItem.Request.Source, queuedItem.Request);

                queuedItem.RetryCount++;

                if (queuedItem.RetryCount >= MaxRetryCount)
                {
                    logger.LogCritical("Log could not be delivered after {MaxRetryCount} attempts. Fallback is required. {@Log}",
                        MaxRetryCount, JsonSerializer.Serialize(queuedItem.Request));
                    continue;
                }

                await queueService.EnqueueAsync(queuedItem, stoppingToken);
            }
        }
    }
}


