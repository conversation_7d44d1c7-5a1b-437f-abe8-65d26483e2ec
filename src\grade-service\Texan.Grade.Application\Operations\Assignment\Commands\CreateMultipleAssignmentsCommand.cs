namespace Texan.Grade.Application.Operations.Assignment.Commands;

public class CreateMultipleAssignmentsCommand
{
    public class Request : IRequest<Result<BulkCreateResult>>
    {
        public List<AssignmentCreateRequest> Assignments { get; set; } = new();
    }

    public class AssignmentCreateRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
        public string CourseClassId { get; set; } = string.Empty;
        public string? WeekId { get; set; }
        public byte Order { get; set; }
        public DateTime AssignedAt { get; set; }
        public DateTime? DueDate { get; set; }
        public string CreatedByUserId { get; set; } = string.Empty;
    }

    public class BulkCreateResult
    {
        public int TotalSubmitted { get; set; }
        public int Successful { get; set; }
        public int Failed { get; set; }
        public List<AssignmentResult> Results { get; set; } = new();
    }

    public class AssignmentResult
    {
        public int Index { get; set; }
        public bool Success { get; set; }
        public string? AssignmentId { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Assignments)
                .NotEmpty().WithMessage("At least one assignment is required.")
                .Must(assignments => assignments.Count <= 50).WithMessage("Cannot create more than 50 assignments at once.");

            RuleForEach(r => r.Assignments).SetValidator(new AssignmentCreateRequestValidator());
        }
    }

    public class AssignmentCreateRequestValidator : AbstractValidator<AssignmentCreateRequest>
    {
        public AssignmentCreateRequestValidator()
        {
            RuleFor(r => r.Name)
                .NotEmpty().WithMessage("Assignment name is required.")
                .MaximumLength(200).WithMessage("Assignment name cannot exceed 200 characters.");
            
            RuleFor(r => r.Details)
                .NotEmpty().WithMessage("Assignment details are required.")
                .MaximumLength(2000).WithMessage("Assignment details cannot exceed 2000 characters.");
            
            RuleFor(r => r.CourseClassId)
                .NotEmpty().WithMessage("Course Class ID is required.");
            
            RuleFor(r => r.CreatedByUserId)
                .NotEmpty().WithMessage("Instructor ID is required.");
            
            RuleFor(r => r.AssignedAt)
                .NotEmpty().WithMessage("Assignment date is required.");
            
            RuleFor(r => r.DueDate)
                .GreaterThan(r => r.AssignedAt)
                .When(r => r.DueDate.HasValue)
                .WithMessage("Due date must be after assignment date.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<BulkCreateResult>>
    {
        public async Task<Result<BulkCreateResult>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Creating {Count} assignments", request.Assignments.Count);
            
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Bulk assignment creation failed validation");
                return validationError;
            }

            var result = new BulkCreateResult { TotalSubmitted = request.Assignments.Count };
            
            var duplicateErrors = await ValidateAssignmentDuplicates(request.Assignments, cancellationToken);
            if (duplicateErrors.Any())
            {
                logger.LogWarning("Bulk assignment creation failed - {Count} duplicates found", duplicateErrors.Count);
                foreach (var (index, error) in duplicateErrors)
                {
                    result.Results.Add(new AssignmentResult
                    {
                        Index = index,
                        Success = false,
                        Errors = new List<string> { error.ErrorMessage }
                    });
                }
                result.Failed = duplicateErrors.Count;
                result.Successful = 0;
                return Result.Success(result);
            }
            
            var assignmentsToCreate = new List<AssignmentEntity>();
            for (int i = 0; i < request.Assignments.Count; i++)
            {
                var assignmentRequest = request.Assignments[i];
                var assignment = new AssignmentEntity
                {
                    Id = Guid.NewGuid(),
                    Name = assignmentRequest.Name,
                    Details = assignmentRequest.Details,
                    CourseClassId = assignmentRequest.CourseClassId,
                    WeekId = assignmentRequest.WeekId,
                    Order = assignmentRequest.Order,
                    AssignedAt = assignmentRequest.AssignedAt,
                    DueDate = assignmentRequest.DueDate,
                    CreatedByUserId = assignmentRequest.CreatedByUserId
                };

                assignmentsToCreate.Add(assignment);
                result.Results.Add(new AssignmentResult
                {
                    Index = i,
                    Success = true,
                    AssignmentId = assignment.Id.ToString()
                });
            }

            try
            {
                await crudService.AddMultipleAsync(assignmentsToCreate.ToArray(), cancellationToken);
                result.Successful = assignmentsToCreate.Count;
                result.Failed = 0;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Bulk assignment creation failed in database");
                foreach (var resultItem in result.Results)
                {
                    resultItem.Success = false;
                    resultItem.AssignmentId = null;
                    resultItem.Errors.Add("Database operation failed.");
                }
                result.Successful = 0;
                result.Failed = request.Assignments.Count;
            }

            logger.LogInformation("Bulk assignment creation completed: {Successful} successful, {Failed} failed", result.Successful, result.Failed);
            return Result.Success(result);
        }

        private async Task<List<(int Index, ValidationError Error)>> ValidateAssignmentDuplicates(
            List<AssignmentCreateRequest> assignments, 
            CancellationToken cancellationToken)
        {
            var errors = new List<(int Index, ValidationError Error)>();
            
            var groupedByNameAndCourse = assignments
                .Select((assignment, index) => new { assignment, index })
                .GroupBy(x => new { x.assignment.Name, x.assignment.CourseClassId });

            foreach (var group in groupedByNameAndCourse)
            {
                if (group.Count() > 1)
                {
                    foreach (var item in group.Skip(1))
                    {
                        var error = new ValidationError
                        {
                            Identifier = $"Assignments[{item.index}].Name",
                            ErrorMessage = $"Duplicate assignment name '{group.Key.Name}' in course class '{group.Key.CourseClassId}' within the batch."
                        };
                        errors.Add((item.index, error));
                    }
                }
            }
            
            var uniqueAssignments = assignments
                .Select((assignment, index) => new { assignment, index })
                .GroupBy(x => new { x.assignment.Name, x.assignment.CourseClassId })
                .Select(g => g.First())
                .ToList();

            foreach (var item in uniqueAssignments)
            {
                var existingAssignment = await crudService
                    .Query<AssignmentEntity>()
                    .FirstOrDefaultAsync(a => a.Name == item.assignment.Name && a.CourseClassId == item.assignment.CourseClassId, cancellationToken);

                if (existingAssignment != null)
                {
                    var error = new ValidationError
                    {
                        Identifier = $"Assignments[{item.index}].Name",
                        ErrorMessage = $"An assignment with name '{item.assignment.Name}' already exists in course class '{item.assignment.CourseClassId}'."
                    };
                    errors.Add((item.index, error));
                }
            }

            return errors;
        }
    }
}