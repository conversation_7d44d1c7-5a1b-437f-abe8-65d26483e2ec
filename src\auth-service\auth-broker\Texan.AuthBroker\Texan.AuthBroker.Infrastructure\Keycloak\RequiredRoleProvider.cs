﻿using Microsoft.Extensions.Configuration;

namespace Texan.AuthBroker.Infrastructure.Keycloak;

internal static class RequiredRoleProvider
{
    private const string ConfigKey = "Keycloak:RequiredRoles";

    internal static IReadOnlyCollection<string> GetRoles(IConfiguration cfg)
    {
        var csv = cfg[ConfigKey];
        if (string.IsNullOrWhiteSpace(csv))
            return Array.Empty<string>();

        return csv
            .Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
            .Distinct(StringComparer.OrdinalIgnoreCase)
            .ToArray();
    }
}