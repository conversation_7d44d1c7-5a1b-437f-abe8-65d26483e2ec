
namespace Texan.Grade.Application.Operations.Grade.Queries;

public class GetGradesByStudentQuery
{
    public class Request : IRequest<Result<List<GradeEntity>>>
    {
        public string StudentId { get; set; } = string.Empty;
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.StudentId)
                .NotEmpty().WithMessage("Student ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<List<GradeEntity>>>
    {
        public async Task<Result<List<GradeEntity>>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Getting grades for student: {StudentId}", request.StudentId);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Grades query failed validation for student: {StudentId}", request.StudentId);
                return validationError;
            }

            var grades = await crudService
                .Query<GradeEntity>()
                .Where(g => g.StudentId == request.StudentId)
                .OrderByDescending(g => g.CreatedAt)
                .ToListAsync(cancellationToken);

            logger.LogInformation("Retrieved {Count} grades for student: {StudentId}", grades.Count, request.StudentId);
            return Result.Success(grades);
        }
    }
}