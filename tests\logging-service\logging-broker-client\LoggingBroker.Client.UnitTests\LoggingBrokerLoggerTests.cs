﻿using FluentAssertions;
using Microsoft.Extensions.Logging;

namespace LoggingBroker.Client.UnitTests;

public class LoggingBrokerLoggerTests
{
    [Fact]
    // Loglama işlemi, minimum seviyeye göre doğru şekilde kuyruğa ekleniyor mu?
    public void Log_Should_Enqueue_When_Level_Is_Enabled()
    {
        // Arrange
        var queue = new LogQueueService(10);
        var options = new LoggingBrokerOptions { MinimumLevel = LogLevel.Information };
        var logger = new LoggingBrokerLogger("TestCat", queue, options);

        // Act
        logger.Log(LogLevel.Information, new EventId(1), "test-message", null, (state, ex) => state!.ToString()!);

        // Assert
        var cts = new CancellationTokenSource(200);
        var items = queue.ReadAllAsync(cts.Token).ToBlockingEnumerable();
        items.Should().ContainSingle(item => item.Message == "test-message");
    }

    [Fact]
    public void Log_Should_Not_Enqueue_When_Level_Disabled()
    {
        var queue = new LogQueueService(10);
        var options = new LoggingBrokerOptions { MinimumLevel = LogLevel.Error };
        var logger = new LoggingBrokerLogger("TestCat", queue, options);

        logger.Log(LogLevel.Information, new EventId(1), "should-not-log", null, (state, ex) => state!.ToString()!);

        var cts = new CancellationTokenSource(200);
        var items = queue.ReadAllAsync(cts.Token).ToBlockingEnumerable();
        items.Should().BeEmpty();
    }

    [Fact]
    // Structured log (yani props) parametreleri doğru aktarılıyor mu?
    public void Log_Should_PassStructuredParams_ToLogRequest()
    {
        var queue = new LogQueueService(10);
        var options = new LoggingBrokerOptions { MinimumLevel = LogLevel.Information };
        var logger = new LoggingBrokerLogger("TestCat", queue, options);

        var state = new List<KeyValuePair<string, object>>
        {
            new("userId", 123),
            new("orderId", "xyz")
        };

        logger.Log(LogLevel.Information, new EventId(1), state, null, (s, e) => "Test Message");

        var log = queue.ReadAllAsync(new CancellationTokenSource(100).Token).ToBlockingEnumerable().Single();
        log.Parameters.Should().ContainKey("userId").WhichValue.Should().Be("123");
        log.Parameters.Should().ContainKey("orderId").And.Subject["orderId"].Should().Be("xyz");
    }
}

// Helper to enumerate IAsyncEnumerable easily in tests
public static class AsyncEnumerableExtensions
{
    public static List<T> ToBlockingEnumerable<T>(this IAsyncEnumerable<T> source)
    {
        return source.ToEnumerable().ToList();
    }

    public static IEnumerable<T> ToEnumerable<T>(this IAsyncEnumerable<T> source)
    {
        return Task.Run(async () => {
            var result = new List<T>();
            await foreach (var item in source)
                result.Add(item);
            return result;
        }).Result;
    }
}