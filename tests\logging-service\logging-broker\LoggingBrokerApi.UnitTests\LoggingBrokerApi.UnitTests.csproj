<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="FluentAssertions" Version="8.2.0" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="NSubstitute" Version="5.3.0" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\..\..\src\logging-service\logging-broker\Texan.LoggingBroker.Application\Texan.LoggingBroker.Application.csproj" />
		<ProjectReference Include="..\..\..\..\src\logging-service\logging-broker\Texan.LoggingBroker.Domain\Texan.LoggingBroker.Domain.csproj" />
		<ProjectReference Include="..\..\..\..\src\logging-service\logging-broker\Texan.LoggingBroker.Infrastructure\Texan.LoggingBroker.Infrastructure.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Using Include="Xunit" />
	</ItemGroup>

</Project>
