# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080
EXPOSE 8081


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["class-management/Texan.ClassManagement.Presentation.WebApi/Texan.ClassManagement.Presentation.Api.csproj", "class-management/Texan.ClassManagement.Presentation.WebApi/"]
COPY ["class-management/Texan.ClassManagement.Application/Texan.ClassManagement.Application.csproj", "class-management/Texan.ClassManagement.Application/"]
COPY ["class-management/Texan.ClassManagement.Domain/Texan.ClassManagement.Domain.csproj", "class-management/Texan.ClassManagement.Domain/"]
COPY ["class-management/Texan.ClassManagement.Infrastructure/Texan.ClassManagement.Infrastructure.csproj", "class-management/Texan.ClassManagement.Infrastructure/"]
RUN dotnet restore "./class-management/Texan.ClassManagement.Presentation.WebApi/Texan.ClassManagement.Presentation.Api.csproj"
COPY . .
WORKDIR "/src/class-management/Texan.ClassManagement.Presentation.WebApi"
RUN dotnet build "./Texan.ClassManagement.Presentation.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Texan.ClassManagement.Presentation.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Texan.ClassManagement.Presentation.Api.dll"]