namespace Texan.Grade.Application.Operations.Assignment.Queries;

public class GetAssignmentsByCourseQuery
{
    public class Request : IRequest<Result<List<AssignmentEntity>>>
    {
        public string CourseClassId { get; set; } = string.Empty;
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.CourseClassId)
                .NotEmpty().WithMessage("Course Class ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<List<AssignmentEntity>>>
    {
        public async Task<Result<List<AssignmentEntity>>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Getting assignments for course: {CourseClassId}", request.CourseClassId);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Assignment query failed validation for course: {CourseClassId}", request.CourseClassId);
                return validationError;
            }

            var assignments = await crudService
                .Query<AssignmentEntity>()
                .Where(a => a.CourseClassId == request.CourseClassId)
                .OrderBy(a => a.Order)
                .ThenBy(a => a.AssignedAt)
                .ToListAsync(cancellationToken);

            logger.LogInformation("Retrieved {Count} assignments for course: {CourseClassId}", assignments.Count, request.CourseClassId);
            return Result.Success(assignments);
        }
    }
}