using Texan.Notification.Presentation.WebApi.WebApiExtensions.Extensions;
using Texan.Notification.Presentation.WebApi.WebApiExtensions.Middlewares;

var builder = WebApplication.CreateBuilder(args);
builder.Configuration.AddEnvironmentVariables();
builder.Services.AddAppServices(builder.Configuration);
var app = builder.Build();
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}
app.UseAppMiddleware();
await app.RunAsync();