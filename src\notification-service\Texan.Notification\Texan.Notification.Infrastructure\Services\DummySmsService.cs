﻿using Ardalis.Result;
using Microsoft.Extensions.Logging;
using Texan.Notification.Application.Interfaces;
using Texan.Notification.Domain.Entities;

internal class DummySmsService(ILogger<DummySmsService> logger) : ISmsService
{
    public Task<Result> SendSmsAsync(SmsRequestEntity smsRequest, CancellationToken cancellationToken)
    {
        logger.LogWarning("[Dummy SMS] To: {PhoneNumber}, Message: {Message}",
            smsRequest.PhoneNumber, smsRequest.Message);

        return Task.FromResult(Result.Success());
    }
}
