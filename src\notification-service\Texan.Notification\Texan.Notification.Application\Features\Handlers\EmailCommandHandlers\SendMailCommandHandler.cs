﻿using Ardalis.Result;
using MediatR;
using Microsoft.Extensions.Logging;
using Texan.Notification.Application.Features.Commands.EmailCommands;
using Texan.Notification.Application.Interfaces;
using Texan.Notification.Domain.Entities;

namespace Texan.Notification.Application.Features.Handlers.EmailCommandHandlers;

public class SendMailCommandHandler(IMailService _mailService, ILogger<SendMailCommandHandler> _logger)
    : IRequestHandler<SendMailCommand, Result>
{
    public async Task<Result> Handle(SendMailCommand request, CancellationToken cancellationToken)
    {
        var mailRequest = new MailRequestEntity
        {
            To = request.To,
            Cc = request.Cc,
            Bcc = request.Bcc,
            Subject = request.Subject,
            Body = request.Body,
            IsHtml = request.IsHtml,
            Attachments = request.Attachments?.Select(a => new MailAttachmentEntity
            {
                FileName = a.FileName,
                Content = a.Content,
                ContentType = a.ContentType
            }).ToList()
        };

        _logger.LogInformation("Handling SendMailCommand for subject: {Subject}", request.Subject);

        var result = await _mailService.SendEmailAsync(mailRequest, cancellationToken);

        return result;
    }
}
