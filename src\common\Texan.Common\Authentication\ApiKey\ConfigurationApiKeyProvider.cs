﻿using Microsoft.Extensions.Configuration;

namespace Texan.Common.Authentication.ApiKey;

public sealed class ConfigurationApiKeyProvider(IConfiguration config) : IApiKeyProvider
{
    public Task<string?> GetApiKeyAsync(string scope, string policy, CancellationToken cancellationToken = default)
    {
        var key = config.GetValue<string>($"ApiKeys:{scope}:{policy}");
        return Task.FromResult(key);
    }
}