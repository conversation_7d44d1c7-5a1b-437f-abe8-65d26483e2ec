﻿namespace Texan.Gateway.Api.Enums
{
    public enum HealthCheckTarget
    {
        Local = 1,
        Container = 2
    }

    public static class HealthCheckTargetExtensions
    {
        public static string GetSectionName(this HealthCheckTarget target)
        {
            return target switch
            {
                HealthCheckTarget.Local => "ServiceHealthChecksLocal",
                HealthCheckTarget.Container => "ServiceHealthChecks",
                _ => throw new ArgumentOutOfRangeException(nameof(target), target, null)
            };
        }
    }
}