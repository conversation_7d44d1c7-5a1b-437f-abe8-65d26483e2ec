namespace Texan.Grade.Presentation.WebApi.Endpoints;

/// <summary>
/// Assignment Attachment API endpoints
/// </summary>
public static class AssignmentAttachmentEndpoints
{
    public static void MapAssignmentAttachmentEndpoints(this IEndpointRouteBuilder app, bool skipAuth = false)
    {
        var group = app.MapGroup("/api/assignments/{assignmentId:guid}/attachments")
            .WithTags("Assignment Attachments");

        
        if (!skipAuth)
        {
            group.RequireAuthorization();
        }

        // POST /api/assignments/{assignmentId}/attachments
        group.MapPost("/", async (Guid assignmentId, [FromBody] CreateAssignmentAttachmentCommand.Request request, IMediator mediator, CancellationToken cancellationToken) =>
        {
            request.AssignmentId = assignmentId; 
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.Created($"/api/attachments/{result.Value}", result.Value);
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
            
            if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                return Results.BadRequest(result.ValidationErrors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("CreateAssignmentAttachment")
        .WithSummary("Create assignment attachment")
        .WithDescription("Adds a file attachment to an assignment")
        .Accepts<CreateAssignmentAttachmentCommand.Request>("application/json")
        .Produces<string>(StatusCodes.Status201Created)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // GET /api/assignments/{assignmentId}/attachments
        group.MapGet("/", async (Guid assignmentId, IMediator mediator, CancellationToken cancellationToken) =>
            {
                var request = new GetAttachmentsByAssignmentQuery.Request { AssignmentId = assignmentId };
                var result = await mediator.Send(request, cancellationToken);
    
                if (result.IsSuccess)
                    return Results.Ok(result.Value);
    
                if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                    return Results.NotFound(result.Errors);
        
                if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                    return Results.BadRequest(result.ValidationErrors);
        
                return Results.BadRequest(result.Errors);
            })
            .WithName("GetAttachmentsByAssignment")
            .WithSummary("Get assignment attachments")
            .WithDescription("Retrieves all file attachments for an assignment. Returns empty array if assignment exists but has no attachments.")
            .Produces<List<AssignmentAttachmentEntity>>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized)
            .ProducesProblem(StatusCodes.Status404NotFound);
        
        // DELETE /api/assignments/{assignmentId}/attachments
        group.MapDelete("/", async (Guid assignmentId, IMediator mediator, CancellationToken cancellationToken) =>
            {
                var request = new DeleteAttachmentsByAssignmentCommand.Request { AssignmentId = assignmentId };
                var result = await mediator.Send(request, cancellationToken);
    
                if (result.IsSuccess)
                    return Results.Ok(new { deletedCount = result.Value });
    
                if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                    return Results.NotFound(result.Errors);
        
                if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                    return Results.BadRequest(result.ValidationErrors);
        
                return Results.BadRequest(result.Errors);
            })
            .WithName("DeleteAllAttachmentsByAssignment")
            .WithSummary("Delete all assignment attachments")
            .WithDescription("Removes all file attachments from an assignment. Returns count of deleted attachments (0 if none existed).")
            .Produces<object>()
            .ProducesProblem(StatusCodes.Status400BadRequest)
            .ProducesProblem(StatusCodes.Status401Unauthorized)
            .ProducesProblem(StatusCodes.Status404NotFound);
        
        
        var attachmentGroup = app.MapGroup("/api/attachments")
            .WithTags("Assignment Attachments");

        if (!skipAuth)
        {
            attachmentGroup.RequireAuthorization();
        }

        // GET /api/attachments/{id}
        attachmentGroup.MapGet("/{id:guid}", async (Guid id, IMediator mediator, CancellationToken cancellationToken) =>
        {
            var request = new GetAttachmentByIdQuery.Request { Id = id };
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.Ok(result.Value);
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("GetAttachmentById")
        .WithSummary("Get attachment by ID")
        .WithDescription("Retrieves a specific attachment by its identifier")
        .Produces<AssignmentAttachmentEntity>()
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // PUT /api/attachments/{id}
        attachmentGroup.MapPut("/{id:guid}", async (Guid id, [FromBody] UpdateAttachmentCommand.Request request, IMediator mediator, CancellationToken cancellationToken) =>
        {
            request.Id = id; 
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.Ok(result.Value);
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
            
            if (result.Status == Ardalis.Result.ResultStatus.Invalid)
                return Results.BadRequest(result.ValidationErrors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("UpdateAttachment")
        .WithSummary("Update attachment")
        .WithDescription("Updates an attachment's filename and file type")
        .Accepts<UpdateAttachmentCommand.Request>("application/json")
        .Produces<AssignmentAttachmentEntity>()
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // DELETE /api/attachments/{id}
        attachmentGroup.MapDelete("/{id:guid}", async (Guid id, IMediator mediator, CancellationToken cancellationToken) =>
        {
            var request = new DeleteAttachmentCommand.Request { Id = id };
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.NoContent();
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("DeleteAttachment")
        .WithSummary("Delete attachment")
        .WithDescription("Removes a specific attachment")
        .Produces(StatusCodes.Status204NoContent)
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);

        // GET /api/attachments/file/{fileId}
        attachmentGroup.MapGet("/file/{fileId}", async (string fileId, IMediator mediator, CancellationToken cancellationToken) =>
        {
            var request = new GetAttachmentByFileIdQuery.Request { FileId = fileId };
            var result = await mediator.Send(request, cancellationToken);
            
            if (result.IsSuccess)
                return Results.Ok(result.Value);
            
            if (result.Status == Ardalis.Result.ResultStatus.NotFound)
                return Results.NotFound(result.Errors);
                
            return Results.BadRequest(result.Errors);
        })
        .WithName("GetAttachmentByFileId")
        .WithSummary("Get attachment by file ID")
        .WithDescription("Retrieves an attachment by its external file identifier")
        .Produces<AssignmentAttachmentEntity>()
        .ProducesProblem(StatusCodes.Status401Unauthorized)
        .ProducesProblem(StatusCodes.Status404NotFound);
    }
}