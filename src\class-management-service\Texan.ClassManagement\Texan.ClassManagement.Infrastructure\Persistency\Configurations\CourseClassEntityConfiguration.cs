﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Infrastructure.Persistency.Configurations
{
    internal class CourseClassEntityConfiguration : IEntityTypeConfiguration<CourseClassEntity>
    {
        public void Configure(EntityTypeBuilder<CourseClassEntity> builder)
        {
            BaseEntityConfiguration.ConfigureBase(builder);
            builder.HasIndex(c => c.Name).IsUnique();
            builder.Property(c => c.Name).IsRequired().HasMaxLength(10);
            builder.Property(c => c.ClassType).IsRequired();
            builder.Property(c => c.CourseId).IsRequired();

            builder.HasOne(c => c.Course)
                .WithMany(cc => cc.CourseClasses)
                .HasForeignKey(c => c.CourseId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.OwnsMany(c => c.Weeks, ownedNavigationBuilder =>
            {
                ownedNavigationBuilder.ToJson();
                ownedNavigationBuilder.OwnsMany(w => w.DaySchedules);
            });
        }
    }
}
