﻿using Texan.Notification.Presentation.WebApi.Endpoints;

namespace Texan.Notification.Presentation.WebApi.WebApiExtensions.Middlewares
{
    public static class WebApiMiddlewareExtensions
    {
        public static WebApplication UseAppMiddleware(this WebApplication app)
        {
            app.UseHttpsRedirection();
            app.UseMiddleware<ExceptionHandlingMiddleware>();
            app.UseAuthentication();
            app.UseAuthorization();
            app.MapNotificationEndpoints();
            app.UseCors("AllowAllOrigins");
            return app;
        }
    }
}
