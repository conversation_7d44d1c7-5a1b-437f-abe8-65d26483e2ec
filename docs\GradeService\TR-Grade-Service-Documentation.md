# Grade Service API

## Genel Bakış

Grade Service, eğitim sistemi için ödev ve not yönetimi sağlayan RESTful API servisidir.

**Te<PERSON> Özellikler:**
- .NET 8 Web API (Minimal APIs)
- CQRS Pattern (MediatR)
- PostgreSQL Database
- JWT Authentication
- Docker Containerization
- Comprehensive Validation

## Hızlı Başlangıç

### Kurulum

```bash
# Servisleri başlat
docker-compose up -d

# API durumunu kontrol et
curl http://localhost:8080/health

# Swagger UI
http://localhost:8080
```

### API Base URL
```
http://localhost:8080
```

## API Endpoints

### 📝 Assignments (8 endpoints)

| Method | Endpoint | Açıklama |
|--------|----------|----------|
| `POST` | `/api/assignments` | Yeni ödev oluştur |
| `GET` | `/api/assignments/{id}` | Ödev detayını getir |
| `PUT` | `/api/assignments/{id}` | Ödev güncelle |
| `DELETE` | `/api/assignments/{id}` | Ödev sil |
| `GET` | `/api/assignments/course/{courseClassId}` | Ders ödevlerini listele |
| `GET` | `/api/assignments/week/{weekId}` | Hafta ödevlerini listele |
| `GET` | `/api/assignments/instructor/{instructorId}` | Öğretmen ödevlerini listele |
| `POST` | `/api/assignments/bulk` | Çoklu ödev oluştur |

### 📊 Grades (7 endpoints)

| Method | Endpoint | Açıklama |
|--------|----------|----------|
| `POST` | `/api/grades` | Yeni not oluştur |
| `GET` | `/api/grades/{id}` | Not detayını getir |
| `PUT` | `/api/grades/{id}` | Not güncelle |
| `DELETE` | `/api/grades/{id}` | Not sil |
| `GET` | `/api/grades/student/{studentId}` | Öğrenci notlarını listele |
| `GET` | `/api/grades/assignment/{assignmentId}` | Ödev notlarını listele |
| `POST` | `/api/grades/bulk` | Çoklu not oluştur |

### 📎 Attachments (7 endpoints)

**Ödev Bazlı:**

| Method | Endpoint | Açıklama |
|--------|----------|----------|
| `POST` | `/api/assignments/{assignmentId}/attachments` | Ek dosya ekle |
| `GET` | `/api/assignments/{assignmentId}/attachments` | Ödev eklerini listele |
| `DELETE` | `/api/assignments/{assignmentId}/attachments` | Ödev eklerini toplu sil |

**Dosya Bazlı:**

| Method | Endpoint | Açıklama |
|--------|----------|----------|
| `GET` | `/api/attachments/{id}` | Ek dosya detayı |
| `PUT` | `/api/attachments/{id}` | Ek dosya güncelle |
| `DELETE` | `/api/attachments/{id}` | Ek dosya sil |
| `GET` | `/api/attachments/file/{fileId}` | File ID ile ek getir |

### 🏥 Health (2 endpoints)

| Method | Endpoint | Açıklama |
|--------|----------|----------|
| `GET` | `/health` | Temel sağlık kontrolü |
| `GET` | `/health/detailed` | Detaylı sağlık raporu |

## Request/Response Örnekleri

### Ödev Oluşturma

**Endpoint:** `POST /api/assignments`

**Request Body:**
```json
{
    "name": "MVC ile Ürün Yönetimi",
    "details": "ASP.NET Core MVC kullanarak ürün CRUD işlemlerini gerçekleştiren web uygulaması geliştirin. Entity Framework Core ve Repository Pattern kullanın.",
    "courseClassId": "csharp-programming-2025",
    "weekId": "week3",
    "order": 1,
    "assignedAt": "2024-01-15T09:00:00Z",
    "dueDate": "2024-01-25T23:59:00Z",
    "createdByUserId": "instructor001"
}
```

**Success Response (201):**
```json
"16e58b23-413d-4808-9465-a7e5df5ba77a"
```

**Validation Error Response (400):**
```json
[
    {
        "identifier": "Name",
        "errorMessage": "Assignment name is required."
    }
]
```

**Business Rule Error (400):**
```json
[
    {
        "identifier": "Name",
        "errorMessage": "An assignment with this name already exists in the course class."
    }
]
```

### Ödev Detayı Getirme

**Endpoint:** `GET /api/assignments/{id}`

**Response:**
```json
{
    "id": "16e58b23-413d-4808-9465-a7e5df5ba77a",
    "name": "MVC ile Ürün Yönetimi",
    "details": "ASP.NET Core MVC kullanarak ürün CRUD işlemlerini gerçekleştiren web uygulaması geliştirin. Entity Framework Core ve Repository Pattern kullanın.",
    "courseClassId": "csharp-programming-2025",
    "weekId": "week3",
    "order": 1,
    "assignedAt": "2024-01-15T09:00:00Z",
    "dueDate": "2024-01-25T23:59:00Z",
    "createdByUserId": "instructor001"
}
```

### Toplu Ödev Oluşturma

**Endpoint:** `POST /api/assignments/bulk`

**Request Body:**
```json
{
    "assignments": [
        {
            "name": "Console Uygulaması - Hesap Makinesi",
            "details": "C# Console Application kullanarak 4 işlem yapabilen hesap makinesi uygulaması geliştirin. Switch-case yapısı kullanın.",
            "courseClassId": "csharp-programming-2025",
            "weekId": "week1",
            "order": 1,
            "assignedAt": "2024-09-01T09:00:00Z",
            "dueDate": "2024-09-05T23:59:59Z",
            "createdByUserId": "instructor001"
        },
        {
            "name": "OOP Prensipleri - Kütüphane Sistemi",
            "details": "Encapsulation, Inheritance ve Polymorphism prensiplerini kullanarak kütüphane yönetim sistemi oluşturun.",
            "courseClassId": "csharp-programming-2025",
            "weekId": "week2",
            "order": 2,
            "assignedAt": "2024-09-08T09:00:00Z",
            "dueDate": "2024-09-15T23:59:59Z",
            "createdByUserId": "instructor001"
        }
    ]
}
```

**Success Response (201 - Tüm işlemler başarılı):**
```json
{
    "totalSubmitted": 1,
    "successful": 1,
    "failed": 0,
    "results": [
        {
            "index": 0,
            "success": true,
            "assignmentId": "guid-here",
            "errors": []
        }
    ]
}
```

**Partial Success Response (200 - Bazı işlemler başarılı):**
```json
{
    "totalSubmitted": 2,
    "successful": 1,
    "failed": 1,
    "results": [
        {
            "index": 0,
            "success": true,
            "assignmentId": "guid-here",
            "errors": []
        },
        {
            "index": 1,
            "success": false,
            "assignmentId": null,
            "errors": [
                {
                    "identifier": "Name",
                    "errorMessage": "Assignment name is required."
                }
            ]
        }
    ]
}
```

### Not Oluşturma

**Endpoint:** `POST /api/grades`

**Request Body:**
```json
{
    "studentId": "student-cs001",
    "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
    "givenByUserId": "instructor001",
    "grade": 88.5,
    "isFinal": false
}
```

**Response:**
```json
"2cfeb1ff-7d38-450c-b6ad-008e7dacbbdc"
```

### Not Detayı Getirme

**Endpoint:** `GET /api/grades/{id}`

**Response:**
```json
{
    "id": "2cfeb1ff-7d38-450c-b6ad-008e7dacbbdc",
    "studentId": "student-cs001",
    "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
    "givenByUserId": "instructor001",
    "grade": 88.5,
    "isFinal": false,
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
}
```

### Ek Dosya Ekleme

**Endpoint:** `POST /api/assignments/{assignmentId}/attachments`

**Request Body:**
```json
{
    "fileId": "csharp-project-template",
    "fileName": "mvc-project-template.zip",
    "fileType": "application/zip"
}
```

**Response:**
```json
"5f2048d2-6d03-43d4-aff2-c269dfff1a59"
```

### Ders Ödevlerini Listele

**Endpoint:** `GET /api/assignments/course/{courseClassId}`

**Response:**
```json
[
    {
        "id": "16e58b23-413d-4808-9465-a7e5df5ba77a",
        "name": "MVC ile Ürün Yönetimi",
        "details": "ASP.NET Core MVC kullanarak ürün CRUD işlemlerini gerçekleştiren web uygulaması geliştirin. Entity Framework Core ve Repository Pattern kullanın.",
        "courseClassId": "csharp-programming-2025",
        "weekId": "week3",
        "order": 1,
        "assignedAt": "2024-01-15T09:00:00Z",
        "dueDate": "2024-01-25T23:59:00Z",
        "createdByUserId": "instructor001"
    },
    {
        "id": "a7b2c3d4-5678-9abc-def0-123456789abc",
        "name": "Web API Geliştirme",
        "details": "RESTful Web API oluşturun. JWT Authentication, Swagger dokumentasyonu ve Unit Test yazın.",
        "courseClassId": "csharp-programming-2025",
        "weekId": "week4",
        "order": 2,
        "assignedAt": "2024-01-22T09:00:00Z",
        "dueDate": "2024-02-01T23:59:00Z",
        "createdByUserId": "instructor001"
    }
]
```

### Öğrenci Notlarını Listele

**Endpoint:** `GET /api/grades/student/{studentId}`

**Response:**
```json
[
    {
        "id": "2cfeb1ff-7d38-450c-b6ad-008e7dacbbdc",
        "studentId": "student-cs001",
        "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
        "givenByUserId": "instructor001",
        "grade": 88.5,
        "isFinal": false,
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
    },
    {
        "id": "3d4e5f6a-7890-bcde-f123-456789abcdef",
        "studentId": "student-cs001",
        "assignmentId": "a7b2c3d4-5678-9abc-def0-123456789abc",
        "givenByUserId": "instructor001",
        "grade": 92.0,
        "isFinal": true,
        "createdAt": "2024-02-01T14:30:00Z",
        "updatedAt": "2024-02-01T14:30:00Z"
    }
]
```

### Ödev Eklerini Listele

**Endpoint:** `GET /api/assignments/{assignmentId}/attachments`

**Response:**
```json
[
    {
        "id": "5f2048d2-6d03-43d4-aff2-c269dfff1a59",
        "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
        "fileId": "csharp-project-template",
        "fileName": "mvc-project-template.zip",
        "fileType": "application/zip"
    },
    {
        "id": "7a8b9c0d-1e2f-3456-7890-abcdef123456",
        "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
        "fileId": "requirements-doc",
        "fileName": "proje-gereksinimleri.pdf",
        "fileType": "application/pdf"
    }
]
```
## İş Kuralları

### Assignment Kuralları
- Assignment isimleri aynı ders sınıfında benzersiz olmalı
- Due date, assigned date'den sonra olmalı (eğer belirtilmişse)
- Details alanı required (boş olamaz)
- Order değeri 0-255 arası byte değer
- CreatedByUserId (instructor) required

### Grade Kuralları
- Öğrenci-ödev kombinasyonu benzersiz olmalı
- Final notlar güncellenemez/silinemez
- Not değeri 0-100 arası olmalı
- GivenByUserId (instructor) required

### Attachment Kuralları
- FileId, FileName, FileType required
- Assignment var olmalı

## Error Handling

### HTTP Status Codes
- `200` - Başarılı işlem
- `201` - Kaynak oluşturuldu
- `204` - Başarılı silme
- `400` - Geçersiz request / Validation hatası
- `401` - Authentication gerekli
- `404` - Kaynak bulunamadı
- `500` - Sunucu hatası

### Error Response Format

**Validation Errors:**
```json
[
    {
        "identifier": "Name",
        "errorMessage": "Assignment name is required."
    }
]
```

**Business Rule Violations:**
```json
[
    {
        "identifier": "Name", 
        "errorMessage": "An assignment with this name already exists in the course class."
    }
]
```

**General Errors:**
```json
[
    "Assignment not found"
]
```

**Note:** Tüm validation hataları ve business rule violations 400 Bad Request status code ile döner.

### Validation Rules

**Assignment Validation:**
- Name: required, max 200 characters, unique within course class
- Details: required, max 2000 characters
- CourseClassId: required
- CreatedByUserId: required (Instructor ID)
- AssignedAt: required
- DueDate: optional, must be after AssignedAt if provided
- Order: byte value (0-255)
- WeekId: optional

**Grade Validation:**
- Grade: required, 0-100 range, max 2 decimal places
- StudentId: required
- AssignmentId: required, must exist
- GivenByUserId: required (Instructor ID)
- IsFinal: boolean

**Attachment Validation:**
- FileId: required
- FileName: required
- FileType: required
- AssignmentId: required, must exist


## Postman Collection

Proje ile birlikte `Texan-Grade-Service.postman_collection.json` dosyası sağlanmıştır.

### Import Etme
1. Postman'ı açın
2. Import → Upload Files
3. JSON dosyasını seçin

### Predefined Variables
- `baseUrl` - API base URL (http://localhost:8080)
- `assignmentId` - Test assignment ID
- `gradeId` - Test grade ID
- `studentId` - Test student ID (örn: student-cs001)
- `instructorId` - Test instructor ID (örn: instructor001)
- `attachmentId` - Test attachment ID
- `fileId` - Test file ID (örn: csharp-project-template)
- `courseId` - Test course ID (örn: csharp-programming-2025)



## Docker Setup
### Commands
```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs grade-api

# Stop services
docker-compose down

# Reset database
docker-compose down -v

# Rebuild and start
docker-compose up --build -d
```

## Health Checks

### Basic Health
```bash
curl http://localhost:8080/health
```

**Response:**
```json
{
    "status": "Healthy"
}
```

### Detailed Health
```bash
curl http://localhost:8080/health/detailed
```

**Response:**
```json
{
    "status": "Healthy",
    "checks": [
        {
            "name": "AppDbContext",
            "status": "Healthy",
            "description": "PostgreSQL database connection is healthy",
            "duration": 13.3795
        }
    ]
}
```
