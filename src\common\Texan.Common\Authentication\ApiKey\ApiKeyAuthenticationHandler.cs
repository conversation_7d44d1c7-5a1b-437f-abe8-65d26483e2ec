﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace Texan.Common.Authentication.ApiKey;

internal class ApiKeyAuthenticationHandler(
    IApiKeyProvider apiKeyProvider,
    IOptionsMonitor<AuthenticationSchemeOptions> options,
    ILoggerFactory loggerFactory,
    UrlEncoder encoder) : AuthenticationHandler<AuthenticationSchemeOptions>(options, loggerFactory, encoder)
{
    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        if (!Request.Headers.TryGetValue(ApiKeyConstants.RequestHeader, out var apiKeyHeaderValues))
        {
            return AuthenticateResult.Fail("API Key was not provided.");
        }

        var providedApiKey = apiKeyHeaderValues.FirstOrDefault();

        if (apiKeyHeaderValues.Count == 0 || string.IsNullOrWhiteSpace(providedApiKey))
        {
            return AuthenticateResult.Fail("Invalid API Key provided.");
        }

        var validApiKey = await apiKeyProvider.GetApiKeyAsync(ApiKeyConstants.ScopeSelf, Scheme.Name);

        if (validApiKey is null)
        {
            return AuthenticateResult.Fail("API Key provider returned null.");
        }

        if (providedApiKey != validApiKey)
        {
            return AuthenticateResult.Fail("Invalid API Key provided.");
        }

        var claims = new Claim[] {
            new(ClaimTypes.Name, "ApiKeyUser"),
            new(ClaimTypes.Role, Scheme.Name),
            new(ApiKeyConstants.RequestHeader, providedApiKey),
        };

        var identity = new ClaimsIdentity(claims, Scheme.Name);
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, Scheme.Name);

        return AuthenticateResult.Success(ticket);
    }
}