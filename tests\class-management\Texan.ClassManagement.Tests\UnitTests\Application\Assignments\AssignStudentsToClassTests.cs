﻿using FluentValidation;
using FluentValidation.Results;
using MockQueryable.Moq;
using Moq;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Application.Operations.Assignments;
using Texan.ClassManagement.Domain.Entities;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Tests.UnitTests.Application.Assignments
{
    public class AssignStudentsToClassTests
    {
        private readonly Mock<ICrudService> _mockCrudService;
        private readonly Mock<IValidator<AssignStudentsToClass.Request>> _mockValidator;
        private readonly AssignStudentsToClass.Handler _handler;

        public AssignStudentsToClassTests()
        {
            _mockCrudService = new Mock<ICrudService>();
            _mockValidator = new Mock<IValidator<AssignStudentsToClass.Request>>();
            _handler = new AssignStudentsToClass.Handler(_mockCrudService.Object, _mockValidator.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ReturnsSuccess()
        {
            // Arrange
            var request = new AssignStudentsToClass.Request
            {
                CourseClassId = "class1",
                StudentUserIds = new List<string> { "student1", "student2" }
            };

            var mockParticipationQueryable = new List<ClassParticipationEntity>()
                .AsQueryable()
                .BuildMockDbSet();
            var fakeList = new List<StudentEntity>
            {
                new StudentEntity { Id = "s1", User = new UserEntity { Id = "student1" } },
                new StudentEntity { Id = "s2", User = new UserEntity { Id = "student2" } }
            };

            var mockStudentQueryable = fakeList.AsQueryable().BuildMockDbSet();

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<AssignStudentsToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(c => c.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new CourseClassEntity { Id = "class1" });

            _mockCrudService.Setup(c => c.GetByIdAsync<UserEntity>(
                request.StudentUserIds[0],
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new UserEntity { Id = "student1" });

            _mockCrudService.Setup(c => c.GetByIdAsync<UserEntity>(
                request.StudentUserIds[1],
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new UserEntity { Id = "student2" });

            _mockCrudService.Setup(c => c.GetAll<StudentEntity>())
                .Returns(mockStudentQueryable.Object);

            _mockCrudService.Setup(c => c.GetAll<ClassParticipationEntity>())
                .Returns(mockParticipationQueryable.Object);

            _mockCrudService.Setup(c => c.AddAsync(
                It.IsAny<ClassParticipationEntity>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((ClassParticipationEntity p, CancellationToken _) =>
                    {
                        p.Id = Guid.NewGuid().ToString();
                        return p;
                    });

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(2, result.Value.Count);
        }

        [Fact]
        public async Task Handle_InvalidRequest_ReturnsValidationError()
        {
            // Arrange
            var request = new AssignStudentsToClass.Request
            {
                CourseClassId = "",
                StudentUserIds = []
            };

            var validationFailures = new List<ValidationFailure>
            {
                new("CourseClassId", "CourseClassId is required."),
                new("StudentUserIds", "StudentUserIds list cannot be empty.")
            };

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<AssignStudentsToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(2, result.ValidationErrors.Count());
        }

        [Fact]
        public async Task Handle_ClassNotFound_ReturnsNotFound()
        {
            // Arrange
            var request = new AssignStudentsToClass.Request
            {
                CourseClassId = "nonexistent",
                StudentUserIds = new() { "student1" }
            };

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<AssignStudentsToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(c => c.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                .ReturnsAsync((CourseClassEntity?)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Class not found.", result.Errors.First());
        }

        [Fact]
        public async Task Handle_StudentUserNotFound_SkipsAndContinues()
        {
            // Arrange
            var request = new AssignStudentsToClass.Request
            {
                CourseClassId = "class1",
                StudentUserIds = new() { "student1", "missingStudent" }
            };

            var fakeList = new List<StudentEntity>
            {
                new StudentEntity
                {
                    Id = "s1",
                    User = new UserEntity
                    {
                        Id = "student1"
                    }
                }
            };
            var mockStudentQueryable = fakeList.AsQueryable().BuildMockDbSet();
            var mockParticipationQueryable = new List<ClassParticipationEntity>()
                .AsQueryable()
                .BuildMockDbSet();

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<AssignStudentsToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(c => c.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new CourseClassEntity { Id = "class1" });

            _mockCrudService.Setup(c => c.GetByIdAsync<UserEntity>(
                request.StudentUserIds[0],
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new UserEntity { Id = "student1" });

            _mockCrudService.Setup(c => c.GetByIdAsync<UserEntity>(
                request.StudentUserIds[1],
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((UserEntity?)null);

            _mockCrudService.Setup(c => c.GetAll<StudentEntity>())
                .Returns(mockStudentQueryable.Object);


            _mockCrudService.Setup(c => c.GetAll<ClassParticipationEntity>())
                .Returns(mockParticipationQueryable.Object);

            _mockCrudService.Setup(c => c.AddAsync(
                It.IsAny<ClassParticipationEntity>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((ClassParticipationEntity p, CancellationToken _) =>
                    {
                        p.Id = Guid.NewGuid().ToString();
                        return p;
                    });

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Single(result.Value);
        }

        [Fact]
        public async Task Handle_AlreadyAssignedStudent_SkipsAndContinues()
        {
            // Arrange
            var request = new AssignStudentsToClass.Request
            {
                CourseClassId = "class1",
                StudentUserIds = new() { "student1" }
            };

            var fakeStudentList = new List<StudentEntity>
            {
                new StudentEntity { Id = "s1", User = new UserEntity { Id = "student1" } }
            };

            var mockStudentQueryable = fakeStudentList.AsQueryable().BuildMockDbSet();

            var fakeParticipationsList = new List<ClassParticipationEntity>
            {
                new ClassParticipationEntity
                {
                    CourseClassId = "class1",
                    UserId = "student1",
                    ClassParticipationType = ClassParticipationType.Student,
                    CourseClass = new CourseClassEntity { Id = "class1" },
                    User = new UserEntity { Id = "student1" }
                }
            };

            var mockParticipationQueryable = fakeParticipationsList.AsQueryable().BuildMockDbSet();

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<AssignStudentsToClass.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(c => c.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new CourseClassEntity { Id = "class1" });

            _mockCrudService.Setup(c => c.GetByIdAsync<UserEntity>(
                request.StudentUserIds[0],
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new UserEntity { Id = "student1" });

            _mockCrudService.Setup(c => c.GetAll<StudentEntity>())
                .Returns(mockStudentQueryable.Object);

            _mockCrudService.Setup(c => c.GetAll<ClassParticipationEntity>())
                .Returns(mockParticipationQueryable.Object);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Empty(result.Value); // no new participation added
        }
    }
}
