﻿using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Domain.Entities
{
    // TP199, BE126 vs.
    public class CourseClassEntity : BaseEntity
    {
        public string Name { get; set; } = null!;
        public ClassType ClassType { get; set; }

        public DateTime? StartTime => Weeks.FirstOrDefault()?
            .DaySchedules.FirstOrDefault()?
            .StartDateTime;

        public DateTime? EndTime => Weeks.LastOrDefault()?
            .DaySchedules.LastOrDefault()?
            .EndDateTime;

        public int? TotalWeekCount => Weeks.Count;

        public int? CourseWeekCount => Weeks
            .Where(x => !x.IsBreakWeek).ToList().Count;

        public int? BreakWeekCount => Weeks
            .Where(x => x.IsBreakWeek).ToList().Count;

        // Foreign Key
        public string CourseId { get; set; } = null!;

        // Navigation
        public CourseEntity Course { get; set; } = null!;

        public ICollection<StudentEntity>? Students { get; set; }
        public ICollection<ClassParticipationEntity>? ClassParticipations { get; set; }
        public ICollection<Calendar.WeekScheduleEntity>? Weeks { get; set; }
    }
}