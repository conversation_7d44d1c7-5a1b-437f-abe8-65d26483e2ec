﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Texan.ClassManagement.Domain.Entities
{
    public class GradeResultEntity : BaseEntity
    {
        public decimal Score { get; set; }

        // Foreign Keys
        public string CourseClassId { get; set; } = null!;

        // Navigation
        [ForeignKey(nameof(CourseClassId))]
        public CourseClassEntity CourseClass { get; set; } = null!;
    }
}