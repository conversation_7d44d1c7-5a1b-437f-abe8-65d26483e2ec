﻿using Ardalis.Result;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Application.Operations.Assignments
{
    public class AssignStudentsToClass
    {
        public class Request : IRequest<Result<List<string>>>
        {
            public string CourseClassId { get; set; } = null!;
            public List<string> StudentUserIds { get; set; } = [];
        }

        public class RequestValidator : AbstractValidator<Request>
        {
            public RequestValidator()
            {
                RuleFor(r => r.CourseClassId)
                    .NotEmpty().WithMessage("CourseClassId is required.");
                RuleFor(r => r.StudentUserIds)
                    .NotEmpty().WithMessage("StudentUserIds list cannot be empty.");
            }
        }

        public class Handler(ICrudService crudService, IValidator<Request> validator) : IRequestHandler<Request, Result<List<string>>>
        {
            public async Task<Result<List<string>>> Handle(Request request, CancellationToken cancellationToken)
            {
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = validationResult.Errors
                        .Select(x => new ValidationError
                        {
                            Identifier = x.PropertyName,
                            ErrorMessage = x.ErrorMessage
                        }).ToList();

                    return Result.Invalid(validationErrors);
                }

                var courseClass = await crudService.GetByIdAsync<CourseClassEntity>(request.CourseClassId, cancellationToken);

                if (courseClass is null)
                {
                    return Result.NotFound("Class not found.");
                }

                var addedParticipationIds = new List<string>();

                foreach (var studentUserId in request.StudentUserIds)
                {
                    var studentUser = await crudService.GetByIdAsync<UserEntity>(studentUserId, cancellationToken);

                    if (studentUser is null)
                    {
                        continue;
                    }

                    var student = await crudService.GetAll<StudentEntity>()
                    .FirstOrDefaultAsync(x => x.User.Id == studentUser.Id, cancellationToken);

                    if (student is null)
                    {
                        continue;
                    }

                    var existingParticipation = await crudService.GetAll<ClassParticipationEntity>()
                        .Include(x => x.CourseClass)
                        .Include(x => x.User)
                        .Where(x => x.User.Id == studentUserId
                            && x.CourseClass.Id == request.CourseClassId
                            && x.ClassParticipationType == Domain.Enums.ClassParticipationType.Student)
                        .FirstOrDefaultAsync(cancellationToken);

                    if (existingParticipation is not null)
                    {
                        continue;
                    }

                    var participation = new ClassParticipationEntity
                    {
                        CourseClassId = request.CourseClassId,
                        UserId = studentUserId,
                        ClassParticipationType = Domain.Enums.ClassParticipationType.Student,
                        ParticipationDate = DateTime.UtcNow
                    };

                    await crudService.AddAsync(participation, cancellationToken);

                    addedParticipationIds.Add(participation.Id);
                }

                return Result.Success(addedParticipationIds);
            }
        }
    }
}