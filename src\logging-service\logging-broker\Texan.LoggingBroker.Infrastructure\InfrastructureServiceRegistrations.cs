﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Texan.LoggingBroker.Application.Services.AsyncQueue;
using Texan.LoggingBroker.Application.Services.Loki;
using Texan.LoggingBroker.Infrastructure.Services.Loki;
using Texan.LoggingBroker.Infrastructure.Services.Queue;

namespace Texan.LoggingBroker.Infrastructure;

public static class InfrastructureServiceRegistrations
{
    public static IServiceCollection AddInfrastructureServices(
        this IServiceCollection services,IConfiguration configuration)
    {
        services.AddHttpClient("LokiClient", client =>
        {
            string lokiUrl = configuration.GetValue<string>("LokiUrl") 
                     ?? throw new InvalidOperationException("Loki URL is missing");

            client.BaseAddress = new Uri(lokiUrl);
            client.Timeout = TimeSpan.FromSeconds(5);
        });

        services.AddScoped<ILogService, LokiService>();
        services.AddSingleton(typeof(IAsyncQueueService<>), typeof(ChannelQueueService<>));

        services.AddHttpContextAccessor();

        return services;
    }
}
