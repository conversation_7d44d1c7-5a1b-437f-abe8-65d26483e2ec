using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Texan.Grade.Domain.Entities;

namespace Texan.Grade.Infrastructure.Configurations;

/// <summary>
/// Entity Framework configuration for AssignmentAttachmentEntity
/// </summary>
public class AssignmentAttachmentConfiguration : IEntityTypeConfiguration<AssignmentAttachmentEntity>
{
    public void Configure(EntityTypeBuilder<AssignmentAttachmentEntity> builder)
    {
        // Table name
        builder.ToTable("AssignmentAttachments");

        // Primary key
        builder.HasKey(aa => aa.Id);

        // Properties
        builder.Property(aa => aa.Id)
            .IsRequired();

        builder.Property(aa => aa.AssignmentId)
            .IsRequired();

        builder.Property(aa => aa.FileId)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(aa => aa.FileName)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(aa => aa.FileType)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(aa => aa.CreatedAt)
            .IsRequired();

        builder.Property(aa => aa.UpdatedAt)
            .IsRequired(false);

        // Indexes for common queries
        builder.HasIndex(aa => aa.AssignmentId)
            .HasDatabaseName("IX_AssignmentAttachments_AssignmentId");

        builder.HasIndex(aa => aa.FileId)
            .IsUnique()
            .HasDatabaseName("IX_AssignmentAttachments_FileId_Unique");
        
    }
}