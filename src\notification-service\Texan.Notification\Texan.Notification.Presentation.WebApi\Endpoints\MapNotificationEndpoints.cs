﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using Texan.Notification.Application.Features.Commands.EmailCommands;
using Texan.Notification.Application.Features.Commands.PushCommands;
using Texan.Notification.Application.Features.Commands.SmsCommands;
using Texan.Notification.Presentation.WebApi.Routes;

namespace Texan.Notification.Presentation.WebApi.Endpoints
{
    public static class NotificationEndpoint
    {
        public static IEndpointRouteBuilder MapNotificationEndpoints(this IEndpointRouteBuilder app)
        {
            app.MapGet(NotificationRoutes.HealthCheck, () =>
            {
                return Results.Ok("Notification API is healthy");
            })
                .WithName("Health")
                .WithTags("Health");
            app.MapPost(NotificationRoutes.SendMail, async (ISender sender, [FromBody] SendMailCommand command, CancellationToken cancellationToken) =>
            {
                await sender.Send(command, cancellationToken);
                return Results.Ok("Mail send");
            }).RequireAuthorization();
            app.MapPost(NotificationRoutes.SendSms, async (ISender sender, [FromBody] SendSmsCommand command, CancellationToken cancellationToken) =>
            {
                var result = await sender.Send(command, cancellationToken);
                return result.IsSuccess ? Results.Ok("Sms send") : Results.BadRequest("Failed to send sms notification.");
            }).RequireAuthorization();

            app.MapPost(NotificationRoutes.SendPush, async (ISender sender, [FromBody] SendPushCommand command, CancellationToken cancellationToken) =>
            {
                var result = await sender.Send(command, cancellationToken);
                return result.IsSuccess ? Results.Ok("Push send") : Results.BadRequest("Failed to send push notification.");
            }).RequireAuthorization();
            return app;
        }
    }
}