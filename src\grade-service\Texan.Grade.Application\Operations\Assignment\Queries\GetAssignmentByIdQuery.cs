namespace Texan.Grade.Application.Operations.Assignment.Queries;

public class GetAssignmentByIdQuery
{
    public class Request : IRequest<Result<AssignmentEntity>>
    {
        public Guid Id { get; set; }
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Id)
                .NotEmpty().WithMessage("Assignment ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<AssignmentEntity>>
    {
        public async Task<Result<AssignmentEntity>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Getting assignment: {AssignmentId}", request.Id);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Assignment query failed validation: {AssignmentId}", request.Id);
                return validationError;
            }

            var assignment = await crudService
                .Query<AssignmentEntity>()
                .FirstOrDefaultAsync(a => a.Id == request.Id, cancellationToken);

            if (assignment is null)
            {
                logger.LogWarning("Assignment not found: {AssignmentId}", request.Id);
                return Result.NotFound("Assignment not found.");
            }

            logger.LogInformation("Assignment retrieved: {AssignmentId}", assignment.Id);
            return Result.Success(assignment);
        }
    }
}