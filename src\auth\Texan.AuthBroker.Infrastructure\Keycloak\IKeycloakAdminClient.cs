﻿using Ardalis.Result;
using Texan.AuthBroker.Application.DTOs;
using Texan.AuthBroker.Domain.Entities;

namespace Texan.AuthBroker.Infrastructure.Keycloak;

public interface IKeycloakAdminClient
{
    Task<Result<string>> CreateUserAsync(CreateUserRequest request);

    Task<Result> CreateRoleAsync(string roleName);

    Task<Result> AssignRoleToUserAsync(string userId, string roleName);

    Task<Result> SetUserActiveAsync(string userId, bool isActive);

    Task<Result> ResetUserPasswordAsync(string userId, string newPassword);

    Task<Result<bool>> CheckRealmExistsAsync(string realmName);

    Task<Result> CreateRealmAsync(Realm realm);

    Task<Result<bool>> CheckClientExistsAsync(string clientId);

    Task<Result> CreateClientAsync(string clientId);

    Task<Result> CreateCustomClaimAsync(string claimName);

    Task<Result> AssignCustomClaimToUserAsync(string userId, CustomClaimRequest request);

    Task<Result<TokenResponse>> LoginAsync(TokenRequest request, CancellationToken cancellationToken = default);
}