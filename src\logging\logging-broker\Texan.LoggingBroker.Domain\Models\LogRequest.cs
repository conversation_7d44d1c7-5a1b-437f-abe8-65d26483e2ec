using Microsoft.Extensions.Logging;
using System.Text.Json.Serialization;

namespace Texan.LoggingBroker.Domain.Models;
public record LogRequest
{
    public string Source { get; init; } = default!;
    [JsonConverter(typeof(LogLevelJsonConverter))]
    public LogLevel LogLevel { get; init; }
    public string Message { get; init; } = default!;
    public Dictionary<string, string>? Parameters { get; init; }
    public long EventUnixTimeMs { get; init; } = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
}
