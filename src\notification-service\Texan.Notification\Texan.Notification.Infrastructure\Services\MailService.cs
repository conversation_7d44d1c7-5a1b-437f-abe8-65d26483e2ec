﻿using Ardalis.Result;
using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MimeKit;
using Texan.Notification.Domain.Entities;
using Texan.Notification.Domain.Settings;

namespace Texan.Notification.Infrastructure.Services;
internal class MailService(IOptions<MailSettings> options, ILogger<MailService> logger) : IMailService
{
    private readonly MailSettings _mailSettings = options.Value;
    public async Task<Result> SendEmailAsync(MailRequestEntity request, CancellationToken cancellationToken = default)
    {
        using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        timeoutCts.CancelAfter(TimeSpan.FromSeconds(10));
        logger.LogInformation("Email sending started. Subject: {Subject}", request.Subject);
        var email = new MimeMessage();
        email.From.Add(new MailboxAddress(_mailSettings.SenderName, _mailSettings.SenderEmail));
        foreach (var to in request.To)
            email.To.Add(MailboxAddress.Parse(to));
        if (request.Cc != null)
        {
            foreach (var cc in request.Cc)
                email.Cc.Add(MailboxAddress.Parse(cc));
        }
        if (request.Bcc != null)
        {
            foreach (var bcc in request.Bcc)
                email.Bcc.Add(MailboxAddress.Parse(bcc));
        }
        email.Subject = request.Subject;
        var bodyBuilder = new BodyBuilder
        {
            HtmlBody = request.IsHtml ? request.Body : null,
            TextBody = !request.IsHtml ? request.Body : null
        };
        if (request.Attachments != null)
        {
            foreach (var attachment in request.Attachments)
            {
                var contentType = ContentType.Parse(attachment.ContentType ?? "application/octet-stream");
                bodyBuilder.Attachments.Add(attachment.FileName, attachment.Content, contentType);
            }
        }
        email.Body = bodyBuilder.ToMessageBody();
        using var smtp = new SmtpClient();
        await smtp.ConnectAsync(_mailSettings.SmtpServer, _mailSettings.Port, SecureSocketOptions.StartTls, timeoutCts.Token);
        await smtp.AuthenticateAsync(_mailSettings.Username, _mailSettings.Password, timeoutCts.Token);
        await smtp.SendAsync(email, timeoutCts.Token);
        await smtp.DisconnectAsync(true, timeoutCts.Token);
        logger.LogInformation("Email sent successfully. To: {Recipients}, Subject: {Subject}", string.Join(", ", request.To), request.Subject);
        return Result.Success();
    }
}