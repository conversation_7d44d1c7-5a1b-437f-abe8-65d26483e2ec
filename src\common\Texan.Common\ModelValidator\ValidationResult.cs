﻿namespace Texan.Common.ModelValidator;

public record ValidationResult
{
    public bool IsValid { get; init; }

    public IDictionary<string, string[]> Errors { get; } = new Dictionary<string, string[]>(StringComparer.OrdinalIgnoreCase);

    private ValidationResult()
    { }

    private ValidationResult(IDictionary<string, string[]> errors)
    {
        Errors = errors;
    }

    public static ValidationResult Valid() => new() { IsValid = true };

    public static ValidationResult Invalid(IDictionary<string, string[]> errors) => new(errors)
    {
        IsValid = false
    };
}