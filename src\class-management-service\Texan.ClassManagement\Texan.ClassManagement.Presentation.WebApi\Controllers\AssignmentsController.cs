﻿using Microsoft.AspNetCore.Mvc;
using Texan.ClassManagement.Application.Operations.Assignments;
using SMediator.Core.Abstractions;
using Ardalis.Result.AspNetCore;
using Microsoft.AspNetCore.Authorization;

namespace Texan.ClassManagement.Presentation.WebApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "coordination-personnel, coordination-manager, admin, sysadmin")]
    public class AssignmentsController : ControllerBase
    {
        private readonly IMediator _mediator;

        public AssignmentsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost("assign-student-to-class")]
        [TranslateResultToActionResult]
        public async Task<Ardalis.Result.IResult> AssignStudentToClass([FromBody] AssignStudentToClass.Request request)
        {
            var result = await _mediator.Send(request);
            return result;
        }

        [HttpPost("assign-students-to-class")]
        [TranslateResultToActionResult]
        public async Task<Ardalis.Result.IResult> AssignStudentsToClass([FromBody] AssignStudentsToClass.Request request)
        {
            var result = await _mediator.Send(request);
            return result;
        }

        [HttpPost("assign-instructor-to-class")]
        [TranslateResultToActionResult]
        public async Task<Ardalis.Result.IResult> AssignInstructorToClass([FromBody] AssignInstructorToClass.Request request, CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(request, cancellationToken);
            return result;
        }

        [HttpPost("assign-assistant-instructor-to-class")]
        [TranslateResultToActionResult]
        public async Task<Ardalis.Result.IResult> AssignAssistantInstructorToClass([FromBody] AssignAssistantInstructorToClass.Request request, CancellationToken cancellationToken)
        {
            var result = await _mediator.Send(request, cancellationToken);
            return result;
        }
    }
}
