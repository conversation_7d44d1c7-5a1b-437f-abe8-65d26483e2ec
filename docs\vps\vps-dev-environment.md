# VPS Development Environment

This document describes how to connect to and configure the VPS development environment.

## Server Details

- Hostname/IP: `**************`

### Users
- `root` : `V3b{bX_}Y_fUYBEA` (DO NOT USE UNTIL STRICTLY NECESSARY)
- `devuser`: `b+gB1u<M5R7'*Q5m` (USE THIS)

## SSH Access

From your local machine, you can connect via:

```bash
ssh devuser@**************
# when prompted, enter the password: b+gB1u<M5R7'*Q5m
```

## Environment Configuration
Place your project code under /home/<USER>/projects/texan.

### Useful Commands
- Check disk usage: `df -h`
- Monitor logs: `tail -f /var/log/syslog`
- Manage services: `systemctl [start|stop|status] <service>`

---
Keep this file up to date with any changes to access credentials or server configuration.