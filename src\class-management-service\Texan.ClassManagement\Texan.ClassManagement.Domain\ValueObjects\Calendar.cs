﻿using System.ComponentModel.DataAnnotations.Schema;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Domain.ValueObjects
{
    public class Calendar
    {
        public class WeekSchedule
        {
            // Haftanın numarası (ör. 1. hafta, 2. hafta, ...)
            public int WeekNumber { get; set; }

            // Bu haftanın ara tatil (break week) olup olmadığı
            public bool IsBreakWeek { get; set; }

            // Haftanın her bir günü için plan (örneğin hafta içi sınıflarda Pazartesi, Salı, Çarşamba için ders bilgisi)
            public ICollection<DaySchedule> DaySchedules { get; set; } = null!;
        }

        // Gün bazında planlama bilgileri
        public class DaySchedule
        {
            // DayOfWeek tipi kullanılarak gün bilgisi (Pazartesi, Salı, vb.)
            [NotMapped]
            public DayOfWeek Day => StartDateTime.DayOfWeek;

            // <PERSON>u günün takvimdeki tipi: Ders, ara tatil veya etüt
            public CalendarEntryType EntryType { get; set; }

            // Ders ya da etüt varsa başlama saati
            public DateTime StartDateTime { get; set; }

            // Ders ya da etüt varsa bitiş saati
            public DateTime EndDateTime { get; set; }
        }
    }
}
