﻿using System.Runtime.CompilerServices;
using System.Threading.Channels;

namespace Texan.Common.AsyncQueue;

internal class AsyncQueueService<TRequest> : IAsyncQueueService<TRequest>
{
    private readonly Channel<TRequest> _channel;

    public AsyncQueueService()
    {
        _channel = Channel.CreateUnbounded<TRequest>();
    }

    public AsyncQueueService(int capacity)
    {
        _channel = Channel.CreateBounded<TRequest>(new BoundedChannelOptions(capacity)
        {
            FullMode = BoundedChannelFullMode.Wait,
            SingleReader = true,
            SingleWriter = true,
        });
    }

    public void Enqueue(TRequest request)
    {
        if (!_channel.Writer.TryWrite(request))
        {
            throw new InvalidOperationException("Failed to enqueue request. The channel is full or closed.");
        }
    }

    public async IAsyncEnumerable<TRequest> ConsumeAsync([EnumeratorCancellation] CancellationToken cancellationToken)
    {
        await foreach (var item in _channel.Reader.ReadAllAsync(cancellationToken))
        {
            yield return item;
        }
    }
}