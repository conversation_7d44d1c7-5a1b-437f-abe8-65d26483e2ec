﻿using Ardalis.Result;
using MediatR;
using System.Text.Json.Serialization;

namespace Texan.Notification.Application.Features.Commands.EmailCommands;
public record SendMailCommand(
        [property: JsonPropertyName("to")] string[] To,
        [property: <PERSON>son<PERSON>ropertyName("subject")] string Subject,
        [property: Json<PERSON>ropertyName("body")] string Body,
        [property: Json<PERSON>ropertyName("isHtml")] bool IsHtml = true,
        [property: JsonPropertyName("cc")] string[]? Cc = null,
        [property: JsonPropertyName("bcc")] string[]? Bcc = null,
        [property: JsonPropertyName("attachments")] List<MailAttachmentCommand>? Attachments = null
    ) : IRequest<Result>;