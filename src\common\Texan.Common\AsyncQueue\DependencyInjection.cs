﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Texan.Common.AsyncQueue;

public static class DependencyInjection
{
    /// <summary>
    /// Registers an <see cref="IAsyncQueueService{TRequest}"/> implementation in the dependency injection container.
    /// </summary>
    /// <typeparam name="TRequest">The type of the request to be queued.</typeparam>
    /// <param name="services">The <see cref="IServiceCollection"/> to add the service to.</param>
    /// <param name="capacity">
    /// The maximum capacity of the queue. If less than or equal to 0, an unbounded queue is used.
    /// </param>
    /// <returns>The <see cref="IServiceCollection"/> for chaining.</returns>
    public static IServiceCollection AddAsyncQueueService<TRequest>(this IServiceCollection services, int capacity = -1)
    {
        services.TryAddSingleton<IAsyncQueueService<TRequest>>(sp =>
        {
            if (capacity <= 0)
            {
                return new AsyncQueueService<TRequest>();
            }
            return new AsyncQueueService<TRequest>(capacity);
        });
        return services;
    }
}