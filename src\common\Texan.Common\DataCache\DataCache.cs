﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;

namespace Texan.Common.DataCache;

internal class DataCache(IMemoryCache cache) : IDataCache
{
    public bool Exists(string cacheKey)
    {
        return cache.TryGetValue(cacheKey, out _);
    }

    public async Task<T> GetOrAddAsync<T>(
        string cacheKey,
        Func<Task<T>> dataFactory,
        TimeSpan? expiration = null)
    {
        if (cache.TryGetValue<T>(cacheKey, out var cached))
        {
            if (cached is not null)
            {
                return cached;
            }

            Invalidate(cacheKey);
        }

        var result = await dataFactory();
        var options = new MemoryCacheEntryOptions();
        if (expiration.HasValue)
            options.SetAbsoluteExpiration(expiration.Value);

        cache.Set(cacheKey, result, options);
        return result;
    }

    public void Invalidate(string cacheKey)
    {
        cache.Remove(cacheKey);
    }
}