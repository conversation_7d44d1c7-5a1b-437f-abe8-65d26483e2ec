namespace Texan.Grade.Application.Operations.Grade.Queries;

public class GetGradeByIdQuery
{
    public class Request : IRequest<Result<GradeEntity>>
    {
        public Guid Id { get; set; }
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.Id)
                .NotEmpty().WithMessage("Grade ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<GradeEntity>>
    {
        public async Task<Result<GradeEntity>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Getting grade: {GradeId}", request.Id);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Grade query failed validation: {GradeId}", request.Id);
                return validationError;
            }

            var grade = await crudService
                .Query<GradeEntity>()
                .FirstOrDefaultAsync(g => g.Id == request.Id, cancellationToken);

            if (grade is null)
            {
                logger.LogWarning("Grade not found: {GradeId}", request.Id);
                return Result.NotFound("Grade not found.");
            }

            logger.LogInformation("Grade retrieved: {GradeId}", grade.Id);
            return Result.Success(grade);
        }
    }
}