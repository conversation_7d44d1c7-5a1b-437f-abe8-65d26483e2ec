﻿# 1. Build image
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# <PERSON><PERSON>leri tek tek kopyala
COPY ./Texan.AuthBroker.Application/Texan.AuthBroker.Application.csproj ./Texan.AuthBroker.Application/
COPY ./Texan.AuthBroker.Domain/Texan.AuthBroker.Domain.csproj ./Texan.AuthBroker.Domain/
COPY ./Texan.AuthBroker.Infrastructure/Texan.AuthBroker.Infrastructure.csproj ./Texan.AuthBroker.Infrastructure/
COPY ./Texan.AuthBroker.Presentation.WebApi/Texan.AuthBroker.Presentation.WebApi.csproj ./Texan.AuthBroker.Presentation.WebApi/

# Restore yap
RUN dotnet restore ./Texan.AuthBroker.Presentation.WebApi/Texan.AuthBroker.Presentation.WebApi.csproj

# Tüm solution içeriğini kopyala
COPY . .

# <PERSON><PERSON><PERSON>na hazırla
WORKDIR /src/Texan.AuthBroker.Presentation.WebApi
RUN dotnet publish -c Release -o /app/publish

# 2. Runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app
COPY --from=build /app/publish .

ENTRYPOINT ["dotnet", "Texan.AuthBroker.Presentation.WebApi.dll"]
