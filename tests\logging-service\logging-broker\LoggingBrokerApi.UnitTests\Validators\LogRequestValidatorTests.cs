﻿using FluentAssertions;
using FluentValidation.TestHelper;
using Microsoft.Extensions.Logging;
using Serilog.Events;
using Texan.LoggingBroker.Application.Operations.SendLog;
using Texan.LoggingBroker.Domain.Models;

namespace LoggingBrokerApi.UnitTests.Validators;

public class LogRequestValidatorTests
{
    private readonly LogRequestValidator _validator = new();

    [Fact]
    public void Validate_Should_HaveError_When_EventTimeIsInFuture()
    {
        var futureTime = DateTimeOffset.UtcNow.AddMinutes(10).ToUnixTimeMilliseconds();

        var model = new LogRequest
        {
            Source = "api",
            Message = "Future log",
            LogLevel = LogLevel.Warning,
            EventUnixTimeMs = futureTime
        };

        // Act
        var result = _validator.TestValidate(model);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.EventUnixTimeMs);
    }


    [Theory]
    [InlineData("MySource", "Some message", LogLevel.Information, true)]
    [InlineData("", "Some message", LogLevel.Information, false)]
    [InlineData("MySource", "", LogLevel.Information, false)]
    public void LogRequest_Should_Validate_RequiredFields(string source, string message, LogLevel level, bool expected)
    {
        // Arrange
        var req = new LogRequest
        {
            Source = source,
            Message = message,
            LogLevel = level,
            EventUnixTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };

        // Act
        var result = _validator.Validate(req);

        // Assert
        result.IsValid.Should().Be(expected);
    }
}
