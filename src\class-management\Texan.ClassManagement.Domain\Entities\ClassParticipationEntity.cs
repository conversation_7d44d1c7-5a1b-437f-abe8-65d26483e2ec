﻿using System.ComponentModel.DataAnnotations.Schema;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Domain.Entities
{
    public class ClassParticipationEntity : BaseEntity
    {
        public DateTime ParticipationDate { get; set; }
        public ClassParticipationType ClassParticipationType { get; set; }

        // Foreign Keys
        public string UserId { get; set; } = null!;

        public string CourseClassId { get; set; } = null!;

        // Navigation
        [ForeignKey(nameof(UserId))]
        public UserEntity User { get; set; } = null!;

        [ForeignKey(nameof(CourseClassId))]
        public CourseClassEntity CourseClass { get; set; } = null!;
    }
}