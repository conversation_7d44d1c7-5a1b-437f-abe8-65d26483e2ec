@Texan.Grade.Presentation.WebApi_HostAddress = http://localhost:8080
@jwtToken = your-jwt-token-here

### Health Check - Basic
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/health

### Health Check - Detailed
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/health/detailed

### =========================
### ASSIGNMENTS
### =========================

### Create Assignment
POST {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/assignments
Content-Type: application/json
Authorization: Bearer {{jwtToken}}

{
  "name": "MVC Product Management",
  "details": "Develop a web application that performs product CRUD operations using ASP.NET Core MVC. Use Entity Framework Core and Repository Pattern.",
  "courseClassId": "csharp-programming-2025",
  "weekId": "week3",
  "order": 1,
  "assignedAt": "2024-01-15T09:00:00Z",
  "dueDate": "2024-01-25T23:59:00Z",
  "createdByUserId": "instructor001"
}

### Get Assignment Details
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/assignments/16e58b23-413d-4808-9465-a7e5df5ba77a
Authorization: Bearer {{jwtToken}}

### Update Assignment
PUT {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/assignments/16e58b23-413d-4808-9465-a7e5df5ba77a
Content-Type: application/json
Authorization: Bearer {{jwtToken}}

{
  "name": "MVC Product Management - Updated",
  "details": "Updated assignment details",
  "courseClassId": "csharp-programming-2025",
  "weekId": "week3",
  "order": 1,
  "assignedAt": "2024-01-15T09:00:00Z",
  "dueDate": "2024-01-25T23:59:00Z",
  "createdByUserId": "instructor001"
}

### Delete Assignment
DELETE {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/assignments/16e58b23-413d-4808-9465-a7e5df5ba77a
Authorization: Bearer {{jwtToken}}

### Get Course Assignments
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/assignments/course/csharp-programming-2025
Authorization: Bearer {{jwtToken}}

### Get Week Assignments
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/assignments/week/week3
Authorization: Bearer {{jwtToken}}

### Get Instructor Assignments
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/assignments/instructor/instructor001
Authorization: Bearer {{jwtToken}}

### Create Multiple Assignments (Bulk)
POST {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/assignments/bulk
Content-Type: application/json
Authorization: Bearer {{jwtToken}}

{
  "assignments": [
    {
      "name": "Console Application - Calculator",
      "details": "Develop a calculator application that can perform 4 operations using C# Console Application. Use switch-case structure.",
      "courseClassId": "csharp-programming-2025",
      "weekId": "week1",
      "order": 1,
      "assignedAt": "2024-09-01T09:00:00Z",
      "dueDate": "2024-09-05T23:59:59Z",
      "createdByUserId": "instructor001"
    },
    {
      "name": "OOP Principles - Library System",
      "details": "Create a library management system using Encapsulation, Inheritance and Polymorphism principles.",
      "courseClassId": "csharp-programming-2025",
      "weekId": "week2",
      "order": 2,
      "assignedAt": "2024-09-08T09:00:00Z",
      "dueDate": "2024-09-15T23:59:59Z",
      "createdByUserId": "instructor001"
    }
  ]
}

### =========================
### GRADES
### =========================

### Create Grade
POST {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/grades
Content-Type: application/json
Authorization: Bearer {{jwtToken}}

{
  "studentId": "student-cs001",
  "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
  "givenByUserId": "instructor001",
  "grade": 88.5,
  "isFinal": false
}

### Get Grade Details
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/grades/2cfeb1ff-7d38-450c-b6ad-008e7dacbbdc
Authorization: Bearer {{jwtToken}}

### Update Grade
PUT {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/grades/2cfeb1ff-7d38-450c-b6ad-008e7dacbbdc
Content-Type: application/json
Authorization: Bearer {{jwtToken}}

{
  "studentId": "student-cs001",
  "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
  "givenByUserId": "instructor001",
  "grade": 92.0,
  "isFinal": false
}

### Delete Grade
DELETE {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/grades/2cfeb1ff-7d38-450c-b6ad-008e7dacbbdc
Authorization: Bearer {{jwtToken}}

### Get Student Grades
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/grades/student/student-cs001
Authorization: Bearer {{jwtToken}}

### Get Assignment Grades
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/grades/assignment/16e58b23-413d-4808-9465-a7e5df5ba77a
Authorization: Bearer {{jwtToken}}

### Create Multiple Grades (Bulk)
POST {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/grades/bulk
Content-Type: application/json
Authorization: Bearer {{jwtToken}}

{
  "grades": [
    {
      "studentId": "student-cs001",
      "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
      "givenByUserId": "instructor001",
      "grade": 88.5,
      "isFinal": false
    },
    {
      "studentId": "student-cs002",
      "assignmentId": "16e58b23-413d-4808-9465-a7e5df5ba77a",
      "givenByUserId": "instructor001",
      "grade": 92.0,
      "isFinal": false
    }
  ]
}

### =========================
### ATTACHMENTS
### =========================

### Add Assignment Attachment
POST {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/assignments/16e58b23-413d-4808-9465-a7e5df5ba77a/attachments
Content-Type: application/json
Authorization: Bearer {{jwtToken}}

{
  "fileId": "csharp-project-template",
  "fileName": "mvc-project-template.zip",
  "fileType": "application/zip"
}

### Get Assignment Attachments
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/assignments/16e58b23-413d-4808-9465-a7e5df5ba77a/attachments
Authorization: Bearer {{jwtToken}}

### Delete Assignment Attachments (Bulk)
DELETE {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/assignments/16e58b23-413d-4808-9465-a7e5df5ba77a/attachments
Authorization: Bearer {{jwtToken}}

### Get Attachment Details
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/attachments/5f2048d2-6d03-43d4-aff2-c269dfff1a59
Authorization: Bearer {{jwtToken}}

### Update Attachment
PUT {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/attachments/5f2048d2-6d03-43d4-aff2-c269dfff1a59
Content-Type: application/json
Authorization: Bearer {{jwtToken}}

{
  "fileId": "updated-file-id",
  "fileName": "updated-filename.zip",
  "fileType": "application/zip"
}

### Delete Attachment
DELETE {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/attachments/5f2048d2-6d03-43d4-aff2-c269dfff1a59
Authorization: Bearer {{jwtToken}}

### Get Attachment by File ID
GET {{Texan.Grade.Presentation.WebApi_HostAddress}}/api/attachments/file/csharp-project-template
Authorization: Bearer {{jwtToken}}