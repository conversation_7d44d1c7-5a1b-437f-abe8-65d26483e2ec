﻿using Ardalis.Result;
using Texan.AuthBroker.Application.DTOs;

namespace Texan.AuthBroker.Application.Services;

public interface IUserAppService
{
    Task<Result<string>> CreateUserAsync(CreateUserRequest request);
    Task<Result> CreateRoleAsync(string roleName);                  
    Task<Result> AssignRoleToUserAsync(string userId, string role); 
    Task<Result> SetActiveAsync(string userId, bool isActive);      
    Task<Result> ResetPasswordAsync(string userId, string password);
    Task<Result> CreateCustomClaimAsync(string claimName);          
    Task<Result> AssignCustomClaimAsync(string userId, CustomClaimRequest req);
    Task<Result<TokenResponse>> LoginAsync(TokenRequest request, CancellationToken ct = default);
}