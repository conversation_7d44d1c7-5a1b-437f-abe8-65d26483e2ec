﻿using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Domain.Entities
{
    // TP, BE, MF, YZ vs.
    public class CourseEntity : BaseEntity
    {
        public string Name { get; set; } = null!;
        public string CodePrefix { get; set; } = null!;
        public Level Level { get; set; }
        public int DurationWeek { get; set; }

        // Navigation
        public ICollection<CourseClassEntity>? CourseClasses { get; set; }
    }
}