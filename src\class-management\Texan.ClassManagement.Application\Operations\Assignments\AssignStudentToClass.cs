﻿using Ardalis.Result;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Application.Operations.Assignments
{
    // TODO : may be updated due to the changes of the models

    public class AssignStudentToClass
    {
        public class Request : IRequest<Result<string>>
        {
            public string CourseClassId { get; set; } = null!;
            public string StudentUserId { get; set; } = null!;
        }

        public class RequestValidator : AbstractValidator<Request>
        {
            public RequestValidator()
            {
                RuleFor(r => r.CourseClassId)
                .NotEmpty().WithMessage("CourseClassId is required.");
                RuleFor(r => r.StudentUserId)
                    .NotEmpty().WithMessage("StudentUserId is required.");
            }
        }

        public class Handler(ICrudService crudService, IValidator<Request> validator) : IRequestHandler<Request, Result<string>>
        {
            public async Task<Result<string>> Handle(Request request, CancellationToken cancellationToken)
            {
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = validationResult.Errors
                        .Select(x => new ValidationError
                        {
                            Identifier = x.PropertyName,
                            ErrorMessage = x.ErrorMessage,
                        }).ToList();

                    return Result.Invalid(validationErrors);
                }

                var courseClass = await crudService.GetByIdAsync<CourseClassEntity>(request.CourseClassId, cancellationToken);

                if (courseClass is null)
                {
                    return Result.NotFound("Class not found.");
                }

                var studentUser = await crudService.GetByIdAsync<UserEntity>(request.StudentUserId, cancellationToken);

                if (studentUser is null)
                {
                    return Result.NotFound("Student user not found.");
                }

                var student = await crudService.GetAll<StudentEntity>().FirstOrDefaultAsync(x => x.User.Id == studentUser.Id, cancellationToken);

                if (student is null)
                {
                    return Result.NotFound("Student not found.");
                }

                var participation = await crudService.GetAll<ClassParticipationEntity>()
                    .Include(x => x.CourseClass)
                    .Include(x => x.User)
                    .Where(x => x.User.Id == request.StudentUserId
                        && x.CourseClass.Id == request.CourseClassId
                        && x.ClassParticipationType == Domain.Enums.ClassParticipationType.Student)
                    .FirstOrDefaultAsync(cancellationToken);

                if (participation is not null)
                {
                    return Result.Conflict();
                }

                participation = new ClassParticipationEntity()
                {
                    CourseClassId = request.CourseClassId,
                    UserId = request.StudentUserId,
                    ClassParticipationType = Domain.Enums.ClassParticipationType.Student,
                    ParticipationDate = DateTime.UtcNow
                };

                await crudService.AddAsync(participation, cancellationToken);

                var result = Result.Success(participation.Id);

                return result;
            }
        }
    }
}