name: 🚀 Auto-deploy to Dev

on:
  push:
    branches: [ dev ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    # pull in the host & user from secrets
    env:
      VPS_HOST: ${{ secrets.DEV_VPS_HOST }}
      VPS_USER: ${{ secrets.DEV_VPS_USER }}

    steps:
      # 1) get the code (so the VPS can pull the right branch)
      - uses: actions/checkout@v3

      # 2) load your deploy key into ssh-agent
      - uses: webfactory/ssh-agent@v0.9.1
        with:
          ssh-private-key: ${{ secrets.DEV_DEPLOY_SSH_KEY }}

      # 3) trust your VPS’s fingerprint
      - name: Add VPS to known_hosts
        run: |
          if ! grep -qE "^($VPS_HOST|\\[$VPS_HOST\\]:[0-9]+) " ~/.ssh/known_hosts; then
            ssh-keyscan -H $VPS_HOST >> ~/.ssh/known_hosts
          fi

      # 4) SSH in & run your deploy commands
      - name: Deploy to Dev server
        run: |
          ssh $VPS_USER@$VPS_HOST << 'EOF'
            set -xe
            cd ~/texan
            git fetch origin dev
            git checkout dev
            git reset --hard origin/dev

            # Deploy services
            cd src
            
            # Create docker-dev-backend network if it doesn't exist
            docker network create texan-dev-backend || true
            #docker network inspect texan-dev-backend >/dev/null 2>&1 || docker network create texan-dev-backend

            # Tear down any old containers (removes conflicts)
            docker-compose -f docker-compose.yml -f docker-compose.override.dev.yml down --remove-orphans

            # Rebuild & start
            docker-compose -f docker-compose.yml -f docker-compose.override.dev.yml up -d --build

          EOF
