﻿using FluentAssertions;
using FluentValidation;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using Texan.LoggingBroker.Application.Services.AsyncQueue;
using Texan.LoggingBroker.Domain.Models;

namespace LoggingBrokerApi.UnitTests.Handlers;

public class LogRequestEnqueueTests
{
    private readonly IValidator<LogRequest> _validator = Substitute.For<IValidator<LogRequest>>();
    private readonly IAsyncQueueService<QueuedLogItem> _queue = Substitute.For<IAsyncQueueService<QueuedLogItem>>();

    [Fact]
    public async Task PostLog_Should_EnqueueLog_When_ValidRequest()
    {
        // Arrange
        var logRequest = new LogRequest
        {
            Source = "test-api",
            LogLevel = LogLevel.Warning,
            Message = "hello",
            EventUnixTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };

        _validator.ValidateAsync(logRequest, Arg.Any<CancellationToken>())
                  .Returns(new FluentValidation.Results.ValidationResult());

        // Act
        var queuedItem = new QueuedLogItem { Request = logRequest };

        await _validator.ValidateAsync(logRequest);
        await _queue.EnqueueAsync(queuedItem);

        // Assert
        await _queue.Received(1).EnqueueAsync(Arg.Is<QueuedLogItem>(item =>
            item.Request.Source == "test-api" &&
            item.Request.Message == "hello"
        ));
    }

    [Fact]
    public async Task PostLog_Should_ThrowValidationException_When_Invalid()
    {
        // Arrange
        var logRequest = new LogRequest
        {
            Source = "",
            LogLevel = LogLevel.Warning,
            Message = "",
            EventUnixTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        };

        _validator.ValidateAsync(logRequest, Arg.Any<CancellationToken>())
                  .Throws(new ValidationException("invalid"));

        // Act
        var act = async () =>
        {
            await _validator.ValidateAsync(logRequest);
            await _queue.EnqueueAsync(new QueuedLogItem { Request = logRequest });
        };

        // Assert
        await act.Should().ThrowAsync<ValidationException>()
                 .WithMessage("*invalid*");

        await _queue.DidNotReceive().EnqueueAsync(Arg.Any<QueuedLogItem>());
    }
    [Fact]
    public async Task PostLog_Should_Enqueue_MultipleLogs_InOrder()
    {
        // Arrange
        var logRequests = Enumerable.Range(0, 5).Select(i => new LogRequest
        {
            Source = $"api-{i}",
            LogLevel = LogLevel.Information,
            Message = $"log-{i}",
            EventUnixTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
        }).ToList();

        _validator.ValidateAsync(Arg.Any<LogRequest>(), Arg.Any<CancellationToken>())
            .Returns(new FluentValidation.Results.ValidationResult());

        // Act
        foreach (var req in logRequests)
        {
            await _validator.ValidateAsync(req);
            await _queue.EnqueueAsync(new QueuedLogItem { Request = req });
        }

        // Assert
        foreach (var req in logRequests)
        {
            await _queue.Received(1).EnqueueAsync(Arg.Is<QueuedLogItem>(item =>
                item.Request.Source == req.Source &&
                item.Request.Message == req.Message
            ));
        }
    }
}
