using Microsoft.EntityFrameworkCore;
using Texan.Grade.Domain.Entities;
using Texan.Grade.Infrastructure.Configurations;

namespace Texan.Grade.Infrastructure.Data;

/// <summary>
/// Database context for the Grade service
/// </summary>
public class AppDbContext : DbContext
{
    public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Assignment table
    /// </summary>
    public DbSet<AssignmentEntity> Assignments { get; set; }

    /// <summary>
    /// Grades table
    /// </summary>
    public DbSet<GradeEntity> Grades { get; set; }

    /// <summary>
    /// Assignment attachments table
    /// </summary>
    public DbSet<AssignmentAttachmentEntity> AssignmentAttachments { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply entity configurations
        modelBuilder.ApplyConfiguration(new AssignmentConfiguration());
        modelBuilder.ApplyConfiguration(new GradeConfiguration());
        modelBuilder.ApplyConfiguration(new AssignmentAttachmentConfiguration());
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Automatically set CreatedAt and UpdatedAt
        var entries = ChangeTracker.Entries<BaseEntity>();

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}