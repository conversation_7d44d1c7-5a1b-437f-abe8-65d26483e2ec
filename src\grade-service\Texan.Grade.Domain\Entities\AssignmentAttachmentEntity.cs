namespace Texan.Grade.Domain.Entities;

/// <summary>
/// Represents a file attachment for an assignment
/// </summary>
public class AssignmentAttachmentEntity : BaseEntity
{
    /// <summary>
    /// Identifier of the assignment this attachment belongs to
    /// </summary>
    public Guid AssignmentId { get; set; }
    
    /// <summary>
    /// External file identifier (from file service)
    /// </summary>
    public string FileId { get; set; } = string.Empty;
    
    /// <summary>
    /// Original name of the uploaded file
    /// </summary>
    public string FileName { get; set; } = string.Empty;
    
    /// <summary>
    /// MIME type or file extension of the file
    /// </summary>
    public string FileType { get; set; } = string.Empty;
}