﻿@Texan.Notification.Presentation.WebApi_HostAddress = http://localhost:5147

### 🔍 GET - Weather Forecast (demo)
GET {{Texan.Notification.Presentation.WebApi_HostAddress}}/weatherforecast
Accept: application/json

###

### Post - SMS Notification
POST {{Texan.Notification.Presentation.WebApi_HostAddress}}/api/sms/send
Content-Type: application/json

{
  "phoneNumber": "+905555555555",
  "message": "Hello from .http test!"
}

###

### Post - Email Notification
POST {{Texan.Notification.Presentation.WebApi_HostAddress}}/api/email/send
Content-Type: application/json

{
  "to": ["<EMAIL>"],
  "subject": "Test Mail via .http",
  "body": "<p>This is a test email body.</p>",
  "isHtml": true
}

###

### Post - Push Notification
POST {{Texan.Notification.Presentation.WebApi_HostAddress}}/api/push/send
Content-Type: application/json

{
  "deviceToken": "YOUR_FIREBASE_DEVICE_TOKEN",
  "title": "Test Notification",
  "body": "This is a push notification sent from .http file"
}