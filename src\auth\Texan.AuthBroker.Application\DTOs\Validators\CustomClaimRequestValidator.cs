﻿using FluentValidation;

namespace Texan.AuthBroker.Application.DTOs.Validators;

public class CustomClaimRequestValidator : AbstractValidator<CustomClaimRequest>
{
    public CustomClaimRequestValidator()
    {
        RuleFor(x => x.ClaimName)
            .NotEmpty().WithMessage("Claim name is required.")
            .MinimumLength(3).WithMessage("Claim name must be at least 3 characters.");

        RuleFor(x => x.ClaimValue)
            .NotEmpty().WithMessage("Claim value is required.");
    }
}