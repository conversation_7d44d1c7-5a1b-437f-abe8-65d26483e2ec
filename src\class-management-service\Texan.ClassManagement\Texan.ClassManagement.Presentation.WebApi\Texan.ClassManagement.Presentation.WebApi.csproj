﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>4bbdc72d-b64a-4447-8302-91d49f8821e7</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.Result" Version="10.1.0" />
    <PackageReference Include="Ardalis.Result.AspNetCore" Version="10.1.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Texan.ClassManagement.Application\Texan.ClassManagement.Application.csproj" />
    <ProjectReference Include="..\Texan.ClassManagement.Infrastructure\Texan.ClassManagement.Infrastructure.csproj" />
  </ItemGroup>

</Project>
