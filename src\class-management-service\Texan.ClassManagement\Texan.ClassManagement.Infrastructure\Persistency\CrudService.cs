﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Texan.ClassManagement.Application.Abstractions;

namespace Texan.ClassManagement.Infrastructure.Persistency
{
    internal class CrudService(DbContext dbContext) : ICrudService
    {
        public async Task<T> AddAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class
        {
            dbContext.Set<T>().Add(entity);
            await dbContext.SaveChangesAsync(cancellationToken);
            return entity;
        }

        public async Task<T[]> AddMultipleAsync<T>(T[] entities, CancellationToken cancellationToken = default) where T : class
        {
            dbContext.Set<T>().AddRange(entities);
            await dbContext.SaveChangesAsync(cancellationToken);
            return entities;
        }

        public async Task DeleteAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class
        {
            dbContext.Set<T>().Remove(entity);
            await dbContext.SaveChangesAsync(cancellationToken);
        }

        public async Task DeleteMultipleAsync<T>(T[] entities, CancellationToken cancellationToken = default) where T : class
        {
            dbContext.Set<T>().RemoveRange(entities);
            await dbContext.SaveChangesAsync(cancellationToken);
        }

        public IQueryable<T> GetAll<T>() where T : class
        {
            return dbContext.Set<T>();
        }

        public async Task<T?> GetByIdAsync<T>(string id, CancellationToken cancellationToken = default) where T : class
        {
            return await dbContext.Set<T>().FindAsync(id, cancellationToken);
        }

        public async Task<IDbContextTransaction> GetTransactionAsync(CancellationToken cancellationToken = default)
        {
            return await dbContext.Database.BeginTransactionAsync(cancellationToken);
        }

        public async Task<T> UpdateAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class
        {
            dbContext.Set<T>().Update(entity);
            await dbContext.SaveChangesAsync(cancellationToken);
            return entity;
        }

        public async Task<T[]> UpdateMultipleAsync<T>(T[] entities, CancellationToken cancellationToken = default) where T : class
        {
            dbContext.Set<T>().UpdateRange(entities);
            await dbContext.SaveChangesAsync(cancellationToken);
            return entities;
        }
    }
}
