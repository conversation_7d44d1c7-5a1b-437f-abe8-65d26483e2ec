version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: texan-grade-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: TexanGradeDb
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - grade-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  grade-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: texan-grade-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=TexanGradeDb;Username=postgres;Password=password
      - Jwt__Key=your-super-secret-jwt-key-that-is-long-enough-32-chars
      - Jwt__Issuer=texan-grade-api
      - Jwt__Audience=texan-grade-clients
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - grade-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  grade-network:
    driver: bridge
