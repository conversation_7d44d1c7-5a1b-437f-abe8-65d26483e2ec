﻿using Ardalis.Result;
using Google.Apis.Logging;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Texan.Notification.Application.Interfaces;
using Texan.Notification.Domain.Entities;
using Texan.Notification.Domain.Settings;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;

internal class TwilioSmsService(IOptions<TwilioSettings> _twilioOptions, ILogger<TwilioSmsService> _logger) : ISmsService
{
    private readonly TwilioSettings _twilio = _twilioOptions.Value;

    public async Task<Result> SendSmsAsync(SmsRequestEntity smsRequest, CancellationToken cancellationToken)
    {
        Console.WriteLine($"DEBUG: UseDummy = {_twilio.UseDummy}");
        if (_twilio.UseDummy == true)
        {
            _logger.LogWarning("[Dummy SMS] To: {PhoneNumber}, Message: {Message}",
                smsRequest.PhoneNumber, smsRequest.Message);
            return Result.Success();
        }
        else
        {
            _logger.LogInformation("SMS sending started. Recipient: {Phone}", smsRequest.PhoneNumber);

            TwilioClient.Init(_twilio.AccountSid, _twilio.AuthToken);

            var sendTask = MessageResource.CreateAsync(
                to: new PhoneNumber(smsRequest.PhoneNumber),
                from: new PhoneNumber(_twilio.FromPhone),
                body: smsRequest.Message
            );

            var timeoutTask = Task.Delay(TimeSpan.FromSeconds(10), cancellationToken);
            var completedTask = await Task.WhenAny(sendTask, timeoutTask);

            if (completedTask == timeoutTask)
            {
                _logger.LogWarning("SMS sending timed out. Recipient: {Phone}", smsRequest.PhoneNumber);
                return Result.Error("SMS sending timed out.");
            }

            var message = await sendTask;

            if (message.ErrorCode != null)
            {
                _logger.LogError("SMS failed. Recipient: {To}, ErrorCode: {ErrorCode}, ErrorMessage: {ErrorMessage}",
                    smsRequest.PhoneNumber, message.ErrorCode, message.ErrorMessage);

                return Result.Error($"Twilio Error: {message.ErrorMessage}");
            }

            _logger.LogInformation("SMS sent successfully. SID={Sid}, Recipient={To}", message.Sid, smsRequest.PhoneNumber);
            return Result.Success();
        }
    }
}