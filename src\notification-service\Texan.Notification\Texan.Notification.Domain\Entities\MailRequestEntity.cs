﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Texan.Notification.Domain.Entities
{
    public class MailRequestEntity
    {
        public string[] To { get; set; } = Array.Empty<string>();
        public string[]? Cc { get; set; }
        public string[]? Bcc { get; set; }

        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public bool IsHtml { get; set; } = true;

        public List<MailAttachmentEntity>? Attachments { get; set; }
    }
}