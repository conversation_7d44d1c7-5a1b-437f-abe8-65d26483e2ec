namespace Texan.Grade.Application.Helpers;

/// <summary>
/// Helper class for validation operations
/// </summary>
public static class ValidationHelper
{
    /// <summary>
    /// Validates a request and returns validation result
    /// </summary>
    /// <typeparam name="T">Request type</typeparam>
    /// <param name="validator">Validator instance</param>
    /// <param name="request">Request to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Result with validation errors if invalid, null if valid</returns>
    public static async Task<Result?> ValidateAsync<T>(IValidator<T> validator, T request, CancellationToken cancellationToken)
    {
        var validationResult = await validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            var validationErrors = validationResult.Errors
                .Select(x => new ValidationError
                {
                    Identifier = x.PropertyName,
                    ErrorMessage = x.ErrorMessage
                })
                .ToList();
            return Result.Invalid(validationErrors);
        }
        
        return null; 
    }
  }