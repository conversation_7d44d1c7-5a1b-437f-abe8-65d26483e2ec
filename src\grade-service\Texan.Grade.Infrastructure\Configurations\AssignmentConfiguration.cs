using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Texan.Grade.Domain.Entities;

namespace Texan.Grade.Infrastructure.Configurations;

/// <summary>
/// Entity Framework configuration for AssignmentEntity
/// </summary>
public class AssignmentConfiguration : IEntityTypeConfiguration<AssignmentEntity>
{
    public void Configure(EntityTypeBuilder<AssignmentEntity> builder)
    {
        // Table name
        builder.ToTable("Assignment");

        // Primary key
        builder.HasKey(a => a.Id);

        // Properties
        builder.Property(a => a.Id)
            .IsRequired();

        builder.Property(a => a.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(a => a.Details)
            .IsRequired()
            .HasMaxLength(2000);

        builder.Property(a => a.CourseClassId)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(a => a.WeekId)
            .HasMaxLength(50);

        builder.Property(a => a.Order)
            .IsRequired();

        builder.Property(a => a.AssignedAt)
            .IsRequired();

        builder.Property(a => a.DueDate)
            .IsRequired(false);

        builder.Property(a => a.CreatedByUserId)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(a => a.CreatedAt)
            .IsRequired();

        builder.Property(a => a.UpdatedAt)
            .IsRequired(false);

        // Indexes for common queries
        builder.HasIndex(a => a.CourseClassId)
            .HasDatabaseName("IX_Assignments_CourseClassId");

        builder.HasIndex(a => a.WeekId)
            .HasDatabaseName("IX_Assignments_WeekId");

        builder.HasIndex(a => a.CreatedByUserId)
            .HasDatabaseName("IX_Assignments_CreatedByUserId");

        builder.HasIndex(a => a.DueDate)
            .HasDatabaseName("IX_Assignments_DueDate");

        builder.HasIndex(a => new { a.CourseClassId, a.Order })
            .HasDatabaseName("IX_Assignments_CourseClass_Order");
    }
}