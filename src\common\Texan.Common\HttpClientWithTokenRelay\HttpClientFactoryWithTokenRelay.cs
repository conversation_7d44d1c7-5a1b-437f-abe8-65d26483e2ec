﻿using Microsoft.AspNetCore.Http;

namespace Texan.Common.HttpClientWithTokenRelay;

internal class HttpClientFactoryWithTokenRelay(IHttpClientFactory httpClientFactory,
    IHttpContextAccessor httpContextAccessor) : IHttpClientFactoryWithTokenRelay
{
    public HttpClient CreateClient(string? clientName = null)
    {
        var httpContext = httpContextAccessor?.HttpContext
            ?? throw new InvalidOperationException("HttpContext is not available. Ensure this service is used in a valid HTTP request context.");

        var token = httpContext.Request.Headers.Authorization.ToString().Replace("Bearer ", string.Empty);

        var client = clientName is null
            ? httpClientFactory.CreateClient()
            : httpClientFactory.CreateClient(clientName);

        client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        return client;
    }
}