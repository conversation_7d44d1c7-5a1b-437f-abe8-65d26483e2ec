﻿using System.Runtime.CompilerServices;
using System.Threading.Channels;
using Texan.LoggingBroker.Application.Services.AsyncQueue;

namespace Texan.LoggingBroker.Infrastructure.Services.Queue;

// BlockingCollection yerine Channel kullandık çünkü:
// - Thread-safe
// - Doğrudan async/await destekli
// - Backpressure desteği var (BoundedChannelOptions ile sınırlandırma yapabiliyoruz)
// - Yüksek performanslı AsyncQueue

public class ChannelQueueService<T> : IAsyncQueueService<T>
{
    private readonly Channel<T> _channel;

    public ChannelQueueService(int capacity = 10000)
    {
        var options = new BoundedChannelOptions(capacity)
        {
            FullMode = BoundedChannelFullMode.Wait // veya DropWrite / DropOldest. Log servisi için wait en doğrusu
        };
        _channel = Channel.CreateBounded<T>(options);
    }

    public async Task EnqueueAsync(T item, CancellationToken cancellationToken = default)
    {
        await _channel.Writer.WriteAsync(item, cancellationToken);
    }

    public async IAsyncEnumerable<T> DequeueAsync([EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        while (await _channel.Reader.WaitToReadAsync(cancellationToken))
        {
            while (_channel.Reader.TryRead(out var item))
            {
                yield return item;
            }
        }
    }
}