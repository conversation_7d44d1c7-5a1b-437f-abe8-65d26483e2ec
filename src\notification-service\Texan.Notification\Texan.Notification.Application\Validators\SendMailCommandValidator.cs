﻿using FluentValidation;
using Texan.Notification.Application.Features.Commands.EmailCommands;

public class SendMailCommandValidator : AbstractValidator<SendMailCommand>
{
    public SendMailCommandValidator()
    {
        RuleForEach(x => x.To)
            .NotEmpty().WithMessage("Recipient email is required.")
            .EmailAddress().WithMessage("Recipient must be a valid email address.");

        RuleForEach(x => x.Cc).EmailAddress().When(x => x.Cc != null);
        RuleForEach(x => x.Bcc).EmailAddress().When(x => x.Bcc != null);

        RuleFor(x => x.Subject)
            .NotEmpty().WithMessage("Subject is required.")
            .MaximumLength(200);

        RuleFor(x => x.Body)
            .NotEmpty().WithMessage("Body is required.");

        RuleForEach(x => x.Attachments).ChildRules(attachments =>
        {
            attachments.RuleFor(a => a.FileName)
                .NotEmpty().WithMessage("Attachment must have a file name.");

            attachments.RuleFor(a => a.Content)
                .NotNull().WithMessage("Attachment content is required.")
                .Must(c => c.Length > 0).WithMessage("Attachment content cannot be empty.");
        }).When(x => x.Attachments != null);
    }
}
