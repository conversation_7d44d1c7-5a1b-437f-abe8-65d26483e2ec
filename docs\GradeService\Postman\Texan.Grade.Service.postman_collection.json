{"info": {"_postman_id": "695cfb98-e0d5-4086-b15b-78d171da01c9", "name": "Texan.Grade.Service", "description": "Complete API collection for C# Programming Grade Service with all endpoints and C# programming assignments", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "37604300", "_collection_link": "https://www.postman.com/kaanss7/workspace/my-workspace/collection/37604300-695cfb98-e0d5-4086-b15b-78d171da01c9?action=share&source=collection_link&creator=37604300"}, "item": [{"name": "Health Checks", "item": [{"name": "Basic Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}, "response": []}, {"name": "Detailed Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health/detailed", "host": ["{{baseUrl}}"], "path": ["health", "detailed"]}}, "response": []}]}, {"name": "Assignments", "item": [{"name": "Create Assignment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"MVC ile Ürün Yönetimi\",\n    \"details\": \"ASP.NET Core MVC kullanarak ürün CRUD işlemlerini gerçekleştiren web uygulaması geliştirin. Entity Framework Core ve Repository Pattern kullanın.\",\n    \"courseClassId\": \"{{courseId}}\",\n    \"weekId\": \"week3\",\n    \"order\": 1,\n    \"assignedAt\": \"2024-01-15T09:00:00Z\",\n    \"dueDate\": \"2024-01-25T23:59:00Z\",\n    \"createdByUserId\": \"{{instructorId}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/assignments", "host": ["{{baseUrl}}"], "path": ["api", "assignments"]}}, "response": []}, {"name": "Get Assignment By Id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assignments/{{assignmentId}}", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "{{assignmentId}}"]}}, "response": []}, {"name": "Update Assignment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Web API Geliştirme - Güncellenmiş\",\n    \"details\": \"RESTful Web API oluşturun. JWT Authentication, Swagger dokumentasyonu ve Unit Test yazın. Repository Pattern kullanın.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/assignments/{{assignmentId}}", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "{{assignmentId}}"]}}, "response": []}, {"name": "Delete Assignment", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/assignments/{{assignmentId}}", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "{{assignmentId}}"]}}, "response": []}, {"name": "Get Assignments By Course", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assignments/course/{{courseId}}", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "course", "{{courseId}}"]}}, "response": []}, {"name": "Get Assignments By Week", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assignments/week/{{weekId}}", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "week", "{{weekId}}"]}}, "response": []}, {"name": "Get Assignments By Instructor", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assignments/instructor/{{instructorId}}", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "instructor", "{{instructorId}}"]}}, "response": []}, {"name": "Create Multiple Assignments", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"assignments\": [\n    {\n      \"name\": \"<PERSON>sol<PERSON>ı - He<PERSON><PERSON>\",\n      \"details\": \"C# Console Application kullanarak 4 işlem yapabilen hesap makinesi uygulaması geliştirin. Switch-case yapısı kullanın.\",\n      \"courseClassId\": \"{{courseId}}\",\n      \"weekId\": \"week1\",\n      \"order\": 1,\n      \"assignedAt\": \"2024-09-01T09:00:00Z\",\n      \"dueDate\": \"2024-09-05T23:59:59Z\",\n      \"createdByUserId\": \"{{instructorId}}\"\n    },\n    {\n      \"name\": \"OOP Prensipleri - Kütüphane Sistemi\",\n      \"details\": \"Encapsulation, Inheritance ve Polymorphism prensiplerini kullanarak kütüphane yönetim sistemi oluşturun.\",\n      \"courseClassId\": \"{{courseId}}\",\n      \"weekId\": \"week2\",\n      \"order\": 2,\n      \"assignedAt\": \"2024-09-08T09:00:00Z\",\n      \"dueDate\": \"2024-09-15T23:59:59Z\",\n      \"createdByUserId\": \"{{instructorId}}\"\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/assignments/bulk", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "bulk"]}}, "response": []}]}, {"name": "Grades", "item": [{"name": "Create Grade", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"studentId\": \"{{studentId}}\",\n  \"assignmentId\": \"{{assignmentId}}\",\n  \"givenByUserId\": \"{{instructorId}}\",\n  \"grade\": 88.5,\n  \"isFinal\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/grades", "host": ["{{baseUrl}}"], "path": ["api", "grades"]}}, "response": []}, {"name": "Get Grade By Id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/grades/{{gradeId}}", "host": ["{{baseUrl}}"], "path": ["api", "grades", "{{gradeId}}"]}}, "response": []}, {"name": "Update Grade", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"grade\": 95.0,\n    \"givenByUserId\": \"{{instructorId}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/grades/{{gradeId}}", "host": ["{{baseUrl}}"], "path": ["api", "grades", "{{gradeId}}"]}}, "response": []}, {"name": "Delete Grade", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/grades/{{gradeId}}", "host": ["{{baseUrl}}"], "path": ["api", "grades", "{{gradeId}}"]}}, "response": []}, {"name": "Get Grades By Student", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/grades/student/{{studentId}}", "host": ["{{baseUrl}}"], "path": ["api", "grades", "student", "{{studentId}}"]}}, "response": []}, {"name": "Get Grades By Assignment", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/grades/assignment/{{assignmentId}}", "host": ["{{baseUrl}}"], "path": ["api", "grades", "assignment", "{{assignmentId}}"]}}, "response": []}, {"name": "Create Multiple Grades", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"grades\": [\n    {\n      \"studentId\": \"student-cs001\",\n      \"assignmentId\": \"{{assignmentId}}\",\n      \"givenByUserId\": \"{{instructorId}}\",\n      \"grade\": 85.0,\n      \"isFinal\": false\n    },\n    {\n      \"studentId\": \"student-cs002\",\n      \"assignmentId\": \"{{assignmentId}}\",\n      \"givenByUserId\": \"{{instructorId}}\",\n      \"grade\": 92.5,\n      \"isFinal\": true\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/grades/bulk", "host": ["{{baseUrl}}"], "path": ["api", "grades", "bulk"]}}, "response": []}]}, {"name": "Assignment Attachments", "item": [{"name": "Create Assignment Attachment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fileId\": \"{{fileId}}\",\n    \"fileName\": \"mvc-project-template.zip\",\n    \"fileType\": \"application/zip\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/assignments/{{assignmentId}}/attachments", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "{{assignmentId}}", "attachments"]}}, "response": []}, {"name": "Get All Attachments for Assignment", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/assignments/{{assignmentId}}/attachments", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "{{assignmentId}}", "attachments"]}}, "response": []}, {"name": "Delete All Attachments for Assignment", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/assignments/{{assignmentId}}/attachments", "host": ["{{baseUrl}}"], "path": ["api", "assignments", "{{assignmentId}}", "attachments"]}}, "response": []}, {"name": "Get Attachment by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/attachments/{{attachmentId}}", "host": ["{{baseUrl}}"], "path": ["api", "attachments", "{{attachmentId}}"]}}, "response": []}, {"name": "Get Attachment by File ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/attachments/file/{{fileId}}", "host": ["{{baseUrl}}"], "path": ["api", "attachments", "file", "{{fileId}}"]}}, "response": []}, {"name": "Update Attachment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fileName\": \"updated-mvc-template.zip\",\n    \"fileType\": \"application/zip\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/attachments/{{attachmentId}}", "host": ["{{baseUrl}}"], "path": ["api", "attachments", "{{attachmentId}}"]}}, "response": []}, {"name": "Delete Single Attachment", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/attachments/{{attachmentId}}", "host": ["{{baseUrl}}"], "path": ["api", "attachments", "{{attachmentId}}"]}}, "response": []}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "default"}, {"key": "assignmentId", "value": "16e58b23-413d-4808-9465-a7e5df5ba77a", "type": "default"}, {"key": "weekId", "value": "week3", "type": "default"}, {"key": "instructorId", "value": "instructor001", "type": "default"}, {"key": "gradeId", "value": "2cfeb1ff-7d38-450c-b6ad-008e7dacbbdc", "type": "default"}, {"key": "studentId", "value": "student-cs001", "type": "default"}, {"key": "attachmentId", "value": "5f2048d2-6d03-43d4-aff2-c269dfff1a59", "type": "default"}, {"key": "fileId", "value": "csharp-project-template", "type": "default"}, {"key": "courseId", "value": "csharp-programming-2025", "type": "default"}]}