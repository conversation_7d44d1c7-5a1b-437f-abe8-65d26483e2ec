﻿using LoggingBroker.Client.BackgorundServices;
using LoggingBroker.Client.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace LoggingBroker.Client.Extensions;

public static class LoggingBrokerServiceCollectionExtensions
{
    public static ILoggingBuilder AddLoggingBroker(this ILoggingBuilder logging)
    {
        var services = logging.Services;
        var configuration = services.BuildServiceProvider().GetRequiredService<IConfiguration>();

        services
          .AddOptions<LoggingBrokerOptions>()
          .Bind(configuration.GetSection("LoggingBroker"))
          .ValidateDataAnnotations()
          .ValidateOnStart();

        services.AddHttpClient<ILogClient, LogHttpClient>()
            .ConfigureHttpClient((sp, client) =>
            {
                var opts = sp.GetRequiredService<IOptions<LoggingBrokerOptions>>().Value;
                client.BaseAddress = new Uri(opts.BaseUrl);
                client.Timeout = TimeSpan.FromSeconds(opts.HttpClientTimeoutSeconds);
            });

        services.AddSingleton(sp =>
            new LogQueueService(sp.GetRequiredService<IOptions<LoggingBrokerOptions>>().Value.MaxQueueSize));

        services.AddSingleton<ILoggerProvider, LoggingBrokerLoggerProvider>();
        services.AddHostedService<LogWorker>();

        return logging;
    }
}