namespace Texan.Grade.Application.Operations.Assignment.Queries;

public class GetAssignmentsByWeekQuery
{
    public class Request : IRequest<Result<List<AssignmentEntity>>>
    {
        public string WeekId { get; set; } = string.Empty;
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.WeekId)
                .NotEmpty().WithMessage("Week ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<List<AssignmentEntity>>>
    {
        public async Task<Result<List<AssignmentEntity>>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Getting assignments for week: {WeekId}", request.WeekId);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Assignment query failed validation for week: {WeekId}", request.WeekId);
                return validationError;
            }

            var assignments = await crudService
                .Query<AssignmentEntity>()
                .Where(a => a.WeekId == request.WeekId)
                .OrderBy(a => a.Order)
                .ThenBy(a => a.AssignedAt)
                .ToListAsync(cancellationToken);

            logger.LogInformation("Retrieved {Count} assignments for week: {WeekId}", assignments.Count, request.WeekId);
            return Result.Success(assignments);
        }
    }
}