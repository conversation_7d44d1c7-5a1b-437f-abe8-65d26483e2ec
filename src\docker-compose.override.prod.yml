services:
  texan.gateway.api:
    container_name: texan.gateway.api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_HTTP_PORTS=5001
    ports:
      - "8000:5001"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
      - ${APPDATA}/Microsoft/UserSecrets:/root/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro
      - ${APPDATA}/ASP.NET/Https:/root/.aspnet/https:ro
    restart: on-failure
    networks:
      - texan-dev-backend

  texan.logging.loki:
    container_name: texan.logging.loki
    ports:
      - "8101:3100"
    volumes:
      - texan.logging.loki-data:/loki
    networks:
      - texan-dev-backend

  texan.logging.grafana:
    container_name: texan.logging.grafana
    ports:
      - "8201:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - texan.logging.grafana-data:/var/lib/grafana
    networks:
      - texan-dev-backend    
      
  texan.logging.api:
    container_name: texan.logging.api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_HTTP_PORTS=5001
    ports:
      - "8001:5001"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
      - ${APPDATA}/Microsoft/UserSecrets:/root/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro
      - ${APPDATA}/ASP.NET/Https:/root/.aspnet/https:ro
    restart: on-failure
    networks:
      - texan-dev-backend

  texan.auth.keycloak.postgres:
    container_name: texan.auth.keycloak.postgres
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: 6dc30b52386c49a096c0cd77863f69a5
    volumes:
      - texan.auth.keycloak.postgres-data:/var/lib/postgresql/data
    ports:
      - "8102:5423"
    restart: on-failure
    networks:
      - texan-dev-backend
 
  texan.auth.keycloak:
    container_name: texan.auth.keycloak
    ports:
      - "8202:8080"
    command:
        - start
        - --http-enabled=true
        - --hostname-strict=false
        - --hostname=keycloak.texan.graviti.dev
    environment:
      KC_DB: postgres
      KC_DB_URL_HOST: texan.auth.keycloak.postgres
      KC_DB_URL_DATABASE: keycloak
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: 6dc30b52386c49a096c0cd77863f69a5
      KC_PROXY_HEADERS: xforwarded
      KC_HTTP_ENABLED: "true"
      KC_HOSTNAME: keycloak.texan.graviti.dev
      KC_HOSTNAME_STRICT: "false"
      KC_HTTPS_PORT: 443
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: d9a18ffd6ce14498a4b889100856479c
    restart: on-failure
    networks:
      - texan-dev-backend
      
  texan.auth.api:
    container_name: texan.auth.api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_HTTP_PORTS=5001
    ports:
      - "8002:5001"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
      - ${APPDATA}/Microsoft/UserSecrets:/root/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro
      - ${APPDATA}/ASP.NET/Https:/root/.aspnet/https:ro
    restart: on-failure
    networks:
      - texan-dev-backend

  texan.classmanagement.api:
    container_name: texan.classmanagement.api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_HTTP_PORTS=5001
    ports:
      - "8003:5001"
    volumes:
      - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
      - ${APPDATA}/Microsoft/UserSecrets:/root/.microsoft/usersecrets:ro
      - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro
      - ${APPDATA}/ASP.NET/Https:/root/.aspnet/https:ro
    restart: on-failure
    networks:
      - texan-dev-backend



volumes:
  texan.logging.loki-data:
  texan.logging.grafana-data:
  texan.auth.keycloak.postgres-data:

networks:
  texan-dev-backend:
    external: true
    name: texan-dev-backend