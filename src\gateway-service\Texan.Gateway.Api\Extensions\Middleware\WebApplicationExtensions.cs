﻿namespace Texan.Gateway.Api.Extensions.Middleware
{
    public static class WebApplicationExtensions
    {
        public static WebApplication UseApplicationMiddleware(this WebApplication app)
        {
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI();
            }
            app.MapHealthCheckEndpoints();
            app.UseHttpsRedirection();
            app.UseAuthorization();
            app.UseCors("AllowAllOrigins");
            app.MapReverseProxy();

            return app;
        }
    }
}
