namespace Texan.Grade.Application.Operations.AssignmentAttachments.Commands;

public class DeleteAttachmentsByAssignmentCommand
{
    public class Request : IRequest<Result<int>>
    {
        public Guid AssignmentId { get; set; }
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.AssignmentId)
                .NotEmpty().WithMessage("Assignment ID is required.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<int>>
    {
        public async Task<Result<int>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Deleting all attachments for assignment: {AssignmentId}", request.AssignmentId);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
                return validationError;
        
            var assignmentExists = await crudService
                .Query<AssignmentEntity>()
                .AnyAsync(a => a.Id == request.AssignmentId, cancellationToken);

            if (!assignmentExists)
                return Result.NotFound("Assignment not found.");
        
            var attachments = await crudService
                .Query<AssignmentAttachmentEntity>()
                .Where(aa => aa.AssignmentId == request.AssignmentId)
                .ToListAsync(cancellationToken);

            if (attachments.Any())
            {
                await crudService.DeleteMultipleAsync(attachments.ToArray(), cancellationToken);
            }

            logger.LogInformation("Deleted {Count} attachments for assignment: {AssignmentId}", attachments.Count, request.AssignmentId);
            return Result.Success(attachments.Count);
        }
    }
}