﻿using Microsoft.EntityFrameworkCore.Storage;

namespace Texan.ClassManagement.Application.Abstractions
{
    public interface ICrudService
    {
        IQueryable<T> GetAll<T>() where T : class;

        Task<T?> GetByIdAsync<T>(string id, CancellationToken cancellationToken = default) where T : class;

        Task<T> AddAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class;

        Task<T[]> AddMultipleAsync<T>(T[] entities, CancellationToken cancellationToken = default) where T : class;

        Task<T> UpdateAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class;

        Task<T[]> UpdateMultipleAsync<T>(T[] entity, CancellationToken cancellationToken = default) where T : class;

        Task DeleteAsync<T>(T entity, CancellationToken cancellationToken = default) where T : class;

        Task DeleteMultipleAsync<T>(T[] entity, CancellationToken cancellationToken = default) where T : class;

        Task<IDbContextTransaction> GetTransactionAsync(CancellationToken cancellationToken = default);
    }
}