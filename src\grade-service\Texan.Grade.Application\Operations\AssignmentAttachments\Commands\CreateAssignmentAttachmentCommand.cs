namespace Texan.Grade.Application.Operations.AssignmentAttachments.Commands;

public class CreateAssignmentAttachmentCommand
{
    public class Request : IRequest<Result<string>>
    {
        public Guid AssignmentId { get; set; }
        public string FileId { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.AssignmentId)
                .NotEmpty().WithMessage("Assignment ID is required.");
            
            RuleFor(r => r.FileId)
                .NotEmpty().WithMessage("File ID is required.")
                .MaximumLength(100).WithMessage("File ID cannot exceed 100 characters.");
            
            RuleFor(r => r.FileName)
                .NotEmpty().WithMessage("File name is required.")
                .MaximumLength(255).WithMessage("File name cannot exceed 255 characters.");
            
            RuleFor(r => r.FileType)
                .NotEmpty().WithMessage("File type is required.")
                .MaximumLength(150).WithMessage("File type cannot exceed 150 characters.");
        }
    }
    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<string>>
    {
        public async Task<Result<string>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Creating attachment for assignment: {AssignmentId}", request.AssignmentId);
        
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
                return validationError;
        
            var assignment = await crudService
                .Query<AssignmentEntity>()
                .FirstOrDefaultAsync(a => a.Id == request.AssignmentId, cancellationToken);

            if (assignment is null)
            {
                return Result.NotFound("Assignment not found.");
            }
        
            var existingAttachment = await crudService
                .Query<AssignmentAttachmentEntity>()
                .FirstOrDefaultAsync(aa => aa.FileId == request.FileId, cancellationToken);

            if (existingAttachment is not null)
            {
                var error = new ValidationError
                {
                    Identifier = nameof(request.FileId),
                    ErrorMessage = "This file is already attached to an assignment."
                };
                return Result.Invalid(error);
            }

            var attachment = new AssignmentAttachmentEntity
            {
                AssignmentId = request.AssignmentId,
                FileId = request.FileId,
                FileName = request.FileName,
                FileType = request.FileType
            };

            await crudService.AddAsync(attachment, cancellationToken);
        
            logger.LogInformation("Attachment created: {AttachmentId}", attachment.Id);
            return Result.Success(attachment.Id.ToString());
        }
    }
}