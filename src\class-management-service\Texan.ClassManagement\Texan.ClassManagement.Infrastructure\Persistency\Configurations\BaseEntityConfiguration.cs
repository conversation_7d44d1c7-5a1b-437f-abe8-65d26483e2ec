﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Infrastructure.Persistency.Configurations
{
    internal static class BaseEntityConfiguration
    {
        public static void ConfigureBase<TEntity>(EntityTypeBuilder<TEntity> builder) where TEntity : BaseEntity
        {
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).ValueGeneratedOnAdd();
        }
    }
}
