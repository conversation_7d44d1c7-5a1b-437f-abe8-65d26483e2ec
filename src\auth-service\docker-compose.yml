services:
  authbroker:
    build:
      context: ./auth-broker/Texan.AuthBroker
      dockerfile: Texan.AuthBroker.Presentation.WebApi/Dockerfile
    container_name: authbroker
    ports:
      - "7000:8080"
    environment:
      ASPNETCORE_ENVIRONMENT: Development
      ASPNETCORE_URLS: http://+:8080

      Keycloak__BaseUrl: http://keycloak:8080/
      Keycloak__AdminUser: admin
      Keycloak__AdminPass: admin
      Keycloak__Realm: texan
      Keycloak__ClientId: test-client
      Keycloak__RequiredRoles: >-
        accounting-personnel,accounting-manager,
        sales-personnel,sales-manager,
        coordination-personnel,coordination-manager,
        instructor-assistant,instructor,
        student,student-graduate,
        intern,
        software-developer,software-team-leader,
        admin,sysadmin
    depends_on:
    - keycloak

  keycloak:
    image: quay.io/keycloak/keycloak:latest
    container_name: keycloak
    restart: unless-stopped
    ports:
      - "8081:8080"
    command:
      - start-dev
      - --http-enabled=true
      - --hostname-strict=false
      - --hostname=keycloak.texan.graviti.dev
      - --proxy-headers=xforwarded
    environment:
      KC_DB: postgres
      KC_DB_URL_HOST: postgres
      KC_DB_URL_DATABASE: keycloak
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: password
      KC_HOSTNAME: localhost
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
    depends_on:
      - postgres
    healthcheck:
      test: ["CMD-SHELL","cat < /dev/tcp/localhost/8080 > /dev/null"]
      interval: 10s
      timeout: 5s
      retries: 12


  postgres:
    image: postgres:15
    container_name: keycloak_postgres
    restart: unless-stopped
    ports:
      - "5433:5432"
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
