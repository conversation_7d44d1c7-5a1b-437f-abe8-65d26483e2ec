﻿using Ardalis.Result;
using FluentValidation;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Domain.Entities;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Application.Operations.CourseClass.Commands
{
    public class CreateOrUpdateCourseClassScheduleCommand
    {
        public class Request : IRequest<Result<string>>
        {
            public string CourseClassId { get; set; } = null!;
            public List<WeekScheduleRequest> Weeks { get; set; } = new List<WeekScheduleRequest>();
        }

        public class WeekScheduleRequest
        {
            public int WeekNumber { get; set; }
            public bool IsBreakWeek { get; set; }
            public List<DayScheduleRequest> DaySchedules { get; set; } = new List<DayScheduleRequest>();
        }

        public class DayScheduleRequest
        {
            public CalendarEntryType EntryType { get; set; }
            public DateTime StartDateTime { get; set; }
            public DateTime EndDateTime { get; set; }
        }

        public class RequestValidator : AbstractValidator<Request>
        {
            public RequestValidator()
            {
                RuleFor(r => r.Weeks)
                    .NotNull().NotEmpty().WithMessage("At least one week schedule is required.");
                RuleForEach(r => r.Weeks).SetValidator(new WeekScheduleRequestValidator());
            }
        }

        public class WeekScheduleRequestValidator : AbstractValidator<WeekScheduleRequest>
        {
            public WeekScheduleRequestValidator()
            {
                RuleFor(r => r.WeekNumber)
                    .GreaterThan(0).WithMessage("Week number must be greater than zero.");
                RuleFor(r => r.DaySchedules)
                    .NotNull().NotEmpty().WithMessage("At least one day schedule is required per week.");
                RuleForEach(r => r.DaySchedules).SetValidator(new DayScheduleRequestValidator());
            }
        }

        public class DayScheduleRequestValidator : AbstractValidator<DayScheduleRequest>
        {
            public DayScheduleRequestValidator()
            {
                RuleFor(r => r.StartDateTime)
                    .LessThan(r => r.EndDateTime)
                    .WithMessage("StartDateTime must be earlier than EndDateTime.");
            }
        }

        public class Handler(ICrudService crudService, IValidator<Request> validator) : IRequestHandler<Request, Result<string>>
        {
            public async Task<Result<string>> Handle(Request request, CancellationToken cancellationToken)
            {
                var validationResult = await validator.ValidateAsync(request, cancellationToken);
                if (!validationResult.IsValid)
                {
                    var validationErrors = validationResult.Errors
                        .Select(x => new ValidationError
                        {
                            Identifier = x.PropertyName,
                            ErrorMessage = x.ErrorMessage
                        })
                        .ToList();
                    return Result.Invalid(validationErrors);
                }

                var courseClass = await crudService.GetByIdAsync<CourseClassEntity>(request.CourseClassId, cancellationToken);
                if (courseClass is null)
                {
                    return Result.NotFound("Course class not found.");
                }

                var weeks = request.Weeks.Select(week => new Calendar.WeekScheduleEntity
                {
                    WeekNumber = week.WeekNumber,
                    IsBreakWeek = week.IsBreakWeek,
                    DaySchedules = week.DaySchedules.Select(ds => new Calendar.DayScheduleEntity
                    {
                        EntryType = ds.EntryType,
                        StartDateTime = ds.StartDateTime,
                        EndDateTime = ds.EndDateTime
                    }).ToList()
                }).ToList();

                courseClass.Weeks = weeks;

                await crudService.UpdateAsync(courseClass, cancellationToken);

                return Result.Success(courseClass.Id);
            }
        }
    }
}