﻿using Ardalis.Result;
using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Logging;
using System.Text;
using Texan.Notification.Domain.Entities;

namespace Texan.Notification.Infrastructure.Services;

internal class PushService(ILogger<PushService> logger) : IPushService
{
    private static bool _isInitialized = false;

    public async Task<Result> SendAsync(PushRequestEntity pushRequestEntity, CancellationToken cancellationToken)
    {
        InitializeFirebase();

        logger.LogInformation("Push notification sending started. DeviceToken: {Token}, Title: {Title}",
            pushRequestEntity.DeviceToken, pushRequestEntity.Title);

        var message = new Message
        {
            Token = pushRequestEntity.DeviceToken,
            Notification = new FirebaseAdmin.Messaging.Notification
            {
                Title = pushRequestEntity.Title,
                Body = pushRequestEntity.Body
            },
        };

        using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        timeoutCts.CancelAfter(TimeSpan.FromSeconds(10));

        var sendTask = FirebaseMessaging.DefaultInstance.SendAsync(message, timeoutCts.Token);
        var timeoutTask = Task.Delay(TimeSpan.FromSeconds(10), timeoutCts.Token);

        var completedTask = await Task.WhenAny(sendTask, timeoutTask);

        if (completedTask == timeoutTask)
        {
            logger.LogWarning("Push notification timed out. DeviceToken: {Token}", pushRequestEntity.DeviceToken);
            pushRequestEntity.IsSuccessful = false;
            return Result.Error("Push notification timed out.");
        }

        var result = await sendTask;
        pushRequestEntity.SentAt = DateTime.UtcNow;
        pushRequestEntity.IsSuccessful = true;

        logger.LogInformation("Push notification sent successfully. FirebaseId: {FirebaseId}", result);
        return Result.Success();
    }
    private void InitializeFirebase()
    {
        if (_isInitialized)
            return;

        var serviceAccountJson = Environment.GetEnvironmentVariable("FIREBASE_SERVICE_ACCOUNT_JSON");

        if (string.IsNullOrWhiteSpace(serviceAccountJson))
            throw new InvalidOperationException("FIREBASE_SERVICE_ACCOUNT_JSON environment variable is not set.");

        using var stream = new MemoryStream(Encoding.UTF8.GetBytes(serviceAccountJson));

        FirebaseApp.Create(new AppOptions
        {
            Credential = GoogleCredential.FromStream(stream)
        });

        _isInitialized = true;
    }
}