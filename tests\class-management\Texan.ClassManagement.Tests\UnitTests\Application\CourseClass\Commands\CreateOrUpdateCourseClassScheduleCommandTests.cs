﻿using FluentValidation;
using FluentValidation.Results;
using Moq;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Application.Operations.CourseClass.Commands;
using Texan.ClassManagement.Domain.Entities;
using Texan.ClassManagement.Domain.Enums;
using Texan.ClassManagement.Domain.ValueObjects;

namespace Texan.ClassManagement.Tests.UnitTests.Application.CourseClass.Commands
{
    public class CreateOrUpdateCourseClassScheduleCommandTests
    {
        private readonly Mock<ICrudService> _mockCrudService;
        private readonly Mock<IValidator<CreateOrUpdateCourseClassScheduleCommand.Request>> _mockValidator;
        private readonly CreateOrUpdateCourseClassScheduleCommand.Handler _handler;

        public CreateOrUpdateCourseClassScheduleCommandTests()
        {
            _mockCrudService = new Mock<ICrudService>();
            _mockValidator = new Mock<IValidator<CreateOrUpdateCourseClassScheduleCommand.Request>>();
            _handler = new CreateOrUpdateCourseClassScheduleCommand.Handler(_mockCrudService.Object, _mockValidator.Object);
        }

        [Fact]
        public async Task Handle_ValidRequest_ShouldUpdateAndReturnSuccess()
        {
            // Arrange
            var request = new CreateOrUpdateCourseClassScheduleCommand.Request
            {
                CourseClassId = "class1",
                Weeks = new List<CreateOrUpdateCourseClassScheduleCommand.WeekScheduleRequest>
                {
                    new CreateOrUpdateCourseClassScheduleCommand.WeekScheduleRequest()
                    {
                        WeekNumber = 1,
                        IsBreakWeek = false,
                        DaySchedules = new List<CreateOrUpdateCourseClassScheduleCommand.DayScheduleRequest>
                            {
                                new CreateOrUpdateCourseClassScheduleCommand.DayScheduleRequest()
                                {
                                    EntryType = CalendarEntryType.Lesson,
                                    StartDateTime = DateTime.Today.AddHours(9),
                                    EndDateTime = DateTime.Today.AddMonths(2)
                                }
                            }
                    }
                }
            };

            var existingCourseClass = new CourseClassEntity
            {
                Id = "class1",
                Weeks = new List<Calendar.WeekSchedule>()
            };

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<CreateOrUpdateCourseClassScheduleCommand.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(s => s.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(existingCourseClass);

            _mockCrudService.Setup(s => s.UpdateAsync(
                It.IsAny<CourseClassEntity>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((CourseClassEntity p, CancellationToken _) =>
                    {
                        p.Id = "class1";
                        return p;
                    });

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal("class1", result.Value);
        }

        [Fact]
        public async Task Handle_InvalidRequest_ShouldReturnValidationErrors()
        {
            // Arrange
            var request = new CreateOrUpdateCourseClassScheduleCommand.Request
            {
                CourseClassId = "",
                Weeks = new List<CreateOrUpdateCourseClassScheduleCommand.WeekScheduleRequest>
                {
                    new()
                    {
                        WeekNumber = 0,
                        IsBreakWeek = false,
                        DaySchedules = new List<CreateOrUpdateCourseClassScheduleCommand.DayScheduleRequest>
                        {
                            new()
                            {
                                EntryType = CalendarEntryType.Lesson,
                                StartDateTime = DateTime.Today.AddHours(9),
                                EndDateTime = DateTime.Today.AddHours(3)
                            }
                        }
                    }
                }
            };
            var validationFailures = new List<ValidationFailure>
            {
                new ValidationFailure(
                    "CourseClassId",
                    "CourseClassId is required."),
                new ValidationFailure(
                    "Weeks[0].WeekNumber",
                    "Week number must be greater than zero."),
                new ValidationFailure(
                    "Weeks[0].DaySchedules[0].StartDateTime",
                    "StartDateTime must be earlier than EndDateTime.")
            };

            _mockValidator.Setup(v => v.ValidateAsync(
                request,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal(3, result.ValidationErrors.Count());
        }

        [Fact]
        public async Task Handle_CourseClassNotFound_ShouldReturnNotFound()
        {
            // Arrange
            var request = new CreateOrUpdateCourseClassScheduleCommand.Request
            {
                CourseClassId = "nonexistent",
                Weeks = new List<CreateOrUpdateCourseClassScheduleCommand.WeekScheduleRequest>
                {
                    new CreateOrUpdateCourseClassScheduleCommand.WeekScheduleRequest()
                    {
                        WeekNumber = 1,
                        IsBreakWeek = false,
                        DaySchedules = new List<CreateOrUpdateCourseClassScheduleCommand.DayScheduleRequest>
                        {
                            new CreateOrUpdateCourseClassScheduleCommand.DayScheduleRequest()
                            {
                                EntryType = CalendarEntryType.Lesson,
                                StartDateTime = DateTime.Today.AddHours(10),
                                EndDateTime = DateTime.Today.AddMonths(2)
                            }
                        }
                    }
                }
            };

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<CreateOrUpdateCourseClassScheduleCommand.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult());

            _mockCrudService.Setup(s => s.GetByIdAsync<CourseClassEntity>(
                request.CourseClassId,
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync((CourseClassEntity)null);

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Course class not found.", result.Errors.First());
        }

        [Fact]
        public async Task Handle_InvalidDayScheduleTime_ShouldReturnValidationError()
        {
            // Arrange
            var request = new CreateOrUpdateCourseClassScheduleCommand.Request
            {
                CourseClassId = "class1",
                Weeks = new List<CreateOrUpdateCourseClassScheduleCommand.WeekScheduleRequest>
                {
                    new CreateOrUpdateCourseClassScheduleCommand.WeekScheduleRequest()
                    {
                        WeekNumber = 1,
                        IsBreakWeek = false,
                        DaySchedules = new List<CreateOrUpdateCourseClassScheduleCommand.DayScheduleRequest>
                        {
                            new CreateOrUpdateCourseClassScheduleCommand.DayScheduleRequest()
                            {
                                EntryType = CalendarEntryType.Lesson,
                                StartDateTime = DateTime.Today.AddHours(10),
                                EndDateTime = DateTime.Today.AddHours(2)
                            }
                        }
                    }
                }
            };

            var validationFailures = new List<ValidationFailure>
            {
                new ValidationFailure(
                    "StartDateTime",
                    "StartDateTime must be earlier than EndDateTime.")
            };

            _mockValidator.Setup(v => v.ValidateAsync(
                It.IsAny<CreateOrUpdateCourseClassScheduleCommand.Request>(),
                It.IsAny<CancellationToken>()))
                    .ReturnsAsync(new ValidationResult(validationFailures));

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("StartDateTime", result.ValidationErrors.First().Identifier);
        }
    }
}
