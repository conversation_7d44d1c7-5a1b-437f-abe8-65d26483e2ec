using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Texan.LoggingBroker.Domain.Models.Converter;
public class LogLevelJsonConverter : JsonConverter<LogLevel>
{
    private static readonly Dictionary<string, LogLevel> Mappings = new(StringComparer.OrdinalIgnoreCase)
    {
        ["trace"] = LogLevel.Trace,
        ["debug"] = LogLevel.Debug,
        ["info"] = LogLevel.Information,
        ["information"] = LogLevel.Information,
        ["warn"] = LogLevel.Warning,
        ["warning"] = LogLevel.Warning,
        ["error"] = LogLevel.Error,
        ["err"] = LogLevel.Error,
        ["critical"] = LogLevel.Critical,
        ["fatal"] = LogLevel.Critical,
        ["none"] = LogLevel.None
    };

    public override LogLevel Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        try
        {
            return reader.TokenType switch
            {
                JsonTokenType.String => ParseFromString(reader.GetString()!),
                JsonTokenType.Number when reader.TryGetInt32(out int intValue) => (LogLevel)intValue,
                _ => LogLevel.None
            };
        }
        catch
        {
            return LogLevel.None;
        }
    }

    private static LogLevel ParseFromString(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return LogLevel.None;

        if (Mappings.TryGetValue(input, out var mappedLevel))
            return mappedLevel;

        if (Enum.TryParse<LogLevel>(input, true, out var parsedLevel))
            return parsedLevel;

        return LogLevel.None;
    }

    public override void Write(Utf8JsonWriter writer, LogLevel value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString());
    }
}
