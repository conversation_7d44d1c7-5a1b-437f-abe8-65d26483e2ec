﻿using FluentValidation;
using Texan.Notification.Application.Features.Commands.SmsCommands;

public class SendSmsCommandValidator : AbstractValidator<SendSmsCommand>
{
    public SendSmsCommandValidator()
    {
        RuleFor(x => x.PhoneNumber)
            .NotEmpty().WithMessage("Phone number is required.")
            .Matches(@"^\+?[1-9]\d{9,14}$") // E.164 format
            .WithMessage("Phone number format is invalid.");

        RuleFor(x => x.Message)
            .NotEmpty().WithMessage("Message content is required.")
            .MaximumLength(160).WithMessage("Message must not exceed 160 characters.");
    }
}
