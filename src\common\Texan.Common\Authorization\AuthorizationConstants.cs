﻿namespace Texan.Common.Authorization;

public static class AuthorizationConstants
{
    public static class Roles
    {
        public const string AccountingPersonnel = "accounting-personnel";
        public const string AccountingManager = "accounting-manager";
        public const string SalesPersonnel = "sales-personnel";
        public const string SalesManager = "sales-manager";
        public const string CoordinationPersonnel = "coordination-personnel";
        public const string CoordinationManager = "coordination-manager";
        public const string InstructorAssistant = "instructor-assistant";
        public const string Instructor = "instructor";
        public const string Student = "student";
        public const string StudentGraduate = "student-graduate";
        public const string Intern = "intern";
        public const string SoftwareDeveloper = "software-developer";
        public const string SoftwareTeamLeader = "software-team-leader";
        public const string Admin = "admin";
        public const string SysAdmin = "sysadmin";

        public static string[] All =>
        [
            AccountingPersonnel,
            AccountingManager,
            SalesPersonnel,
            SalesManager,
            CoordinationPersonnel,
            CoordinationManager,
            InstructorAssistant,
            Instructor,
            Student,
            StudentGraduate,
            Intern,
            SoftwareDeveloper,
            SoftwareTeamLeader,
            Admin,
            SysAdmin
        ];
    }

    public static class Policies
    {
        public const string RequiredSubClaim = "required-sub-claim";

        public static string[] All =>
        [
            RequiredSubClaim,
        ];
    }
}