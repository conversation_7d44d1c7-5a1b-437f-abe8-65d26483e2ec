﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace Texan.Common.HttpClientWithTokenRelay;

public static class DependencyInjection
{
    public static IServiceCollection AddHttpClientFactoryWithTokenRelay(this IServiceCollection services)
    {
        services.AddHttpContextAccessor();
        services.TryAddScoped<IHttpClientFactoryWithTokenRelay, HttpClientFactoryWithTokenRelay>();
        return services;
    }
}