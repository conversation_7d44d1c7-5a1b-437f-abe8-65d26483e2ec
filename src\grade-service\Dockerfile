FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["Texan.Grade.Presentation.WebApi/Texan.Grade.Presentation.WebApi.csproj", "Texan.Grade.Presentation.WebApi/"]
COPY ["Texan.Grade.Application/Texan.Grade.Application.csproj", "Texan.Grade.Application/"]
COPY ["Texan.Grade.Infrastructure/Texan.Grade.Infrastructure.csproj", "Texan.Grade.Infrastructure/"]
COPY ["Texan.Grade.Domain/Texan.Grade.Domain.csproj", "Texan.Grade.Domain/"]

# Restore dependencies
RUN dotnet restore "Texan.Grade.Presentation.WebApi/Texan.Grade.Presentation.WebApi.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/Texan.Grade.Presentation.WebApi"
RUN dotnet build "Texan.Grade.Presentation.WebApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Texan.Grade.Presentation.WebApi.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Texan.Grade.Presentation.WebApi.dll"]