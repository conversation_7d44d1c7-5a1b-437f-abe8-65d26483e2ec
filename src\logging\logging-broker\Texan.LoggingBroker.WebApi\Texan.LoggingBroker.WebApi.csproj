<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Ardalis.Result" Version="10.1.0" />
		<PackageReference Include="FluentValidation" Version="11.11.0" />
		<PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
		<PackageReference Include="MediatR" Version="12.5.0" />
		<PackageReference Include="Polly" Version="8.5.2" />
		<PackageReference Include="Serilog" Version="4.2.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.Grafana.Loki" Version="8.3.0" />
		<PackageReference Include="Serilog.Sinks.Http" Version="9.1.0" />

	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Texan.LoggingBroker.Application\Texan.LoggingBroker.Application.csproj" />
	  <ProjectReference Include="..\Texan.LoggingBroker.Domain\Texan.LoggingBroker.Domain.csproj" />
	  <ProjectReference Include="..\Texan.LoggingBroker.Infrastructure\Texan.LoggingBroker.Infrastructure.csproj" />
	</ItemGroup>

	<PropertyGroup>
		<PreserveCompilationContext>true</PreserveCompilationContext>
	</PropertyGroup>

</Project>
