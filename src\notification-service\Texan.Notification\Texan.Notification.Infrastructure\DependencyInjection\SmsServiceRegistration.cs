﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Texan.Notification.Application.Interfaces;
using Texan.Notification.Domain.Settings;
using Texan.Notification.Infrastructure.Services;

namespace Texan.Notification.Infrastructure.DependencyInjection;

public static class SmsServiceRegistration
{
    public static IServiceCollection AddDummyOrTwilioSmsService(this IServiceCollection services, IConfiguration configuration)
    {
        var twilioSection = configuration.GetSection("Twilio");
        var twilio = twilioSection.Get<TwilioSettings>();

        if (twilio?.UseDummy == true)
        {
            services.AddScoped<ISmsService, DummySmsService>();
        }
        else
        {
            services.AddScoped<ISmsService, TwilioSmsService>();
            services.Configure<TwilioSettings>(twilioSection);
        }

        return services;
    }
}

