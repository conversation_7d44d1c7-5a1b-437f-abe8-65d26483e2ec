﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Texan.Common.LoggingMiddlewares;

internal sealed class ErrorLoggingMiddleware(RequestDelegate next, ILogger<ErrorLoggingMiddleware> logger)
{
    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An unhandled error occurred while processing the request. Trace Id: {RequestTraceId}, Method: {Method}, Path: {Path}, QueryString: {QueryString}, Remote Ip: {RemoteIp}",
                context.TraceIdentifier,
                context.Request.Method,
                context.Request.Path,
                context.Request.QueryString,
                context.Connection.RemoteIpAddress?.ToString());

            context.Response.StatusCode = 500;
        }
    }
}