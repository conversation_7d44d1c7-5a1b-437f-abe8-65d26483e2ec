<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.Result.AspNetCore" Version="10.1.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Texan.AuthBroker.Application\Texan.AuthBroker.Application.csproj" />
    <ProjectReference Include="..\Texan.AuthBroker.Infrastructure\Texan.AuthBroker.Infrastructure.csproj" />
  </ItemGroup>

</Project>
