﻿using Ardalis.Result;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Domain.Entities;
using Texan.ClassManagement.Domain.Enums;

namespace Texan.ClassManagement.Application.Operations.CourseClass.Commands
{
    public class CreateCourseClassCommand
    {
        public class Request : IRequest<Result<string>>
        {
            public string Name { get; set; } = null!;
            public ClassType ClassType { get; set; }
            public string CourseId { get; set; } = null!;
        }

        public class RequestValidator : AbstractValidator<Request>
        {
            public RequestValidator()
            {
                RuleFor(r => r.Name)
                    .NotEmpty().WithMessage("Class name is required.");
                RuleFor(r => r.CourseId)
                    .NotEmpty().WithMessage("Id is required.");
            }
        }

        public class Handler(ICrudService crudService, IValidator<Request> validator) : IRequestHandler<Request, Result<string>>
        {
            public async Task<Result<string>> Handle(Request request, CancellationToken cancellationToken)
            {
                var validationResult = await validator.ValidateAsync(request, cancellationToken);
                if (!validationResult.IsValid)
                {
                    var validationErrors = validationResult.Errors
                        .Select(x => new ValidationError
                        {
                            Identifier = x.PropertyName,
                            ErrorMessage = x.ErrorMessage
                        })
                        .ToList();
                    return Result.Invalid(validationErrors);
                }

                var course = await crudService.GetByIdAsync<CourseEntity>(request.CourseId, cancellationToken);
                if (course is null)
                {
                    return Result.NotFound("Course not found.");
                }

                var duplicate = await crudService
                    .GetAll<CourseClassEntity>()
                    .Where(cc => cc.Name == request.Name)
                    .FirstOrDefaultAsync(cancellationToken);

                if (duplicate is not null)
                {
                    var error = new ValidationError
                    {
                        Identifier = nameof(request.Name),
                        ErrorMessage = "A class with this name already exists for the specified course."
                    };
                    return Result.Invalid(error);
                }

                var courseClass = new CourseClassEntity
                {
                    Name = request.Name,
                    ClassType = request.ClassType,
                    CourseId = request.CourseId
                };

                await crudService.AddAsync(courseClass, cancellationToken);

                return Result.Success(courseClass.Id);
            }
        }
    }
}