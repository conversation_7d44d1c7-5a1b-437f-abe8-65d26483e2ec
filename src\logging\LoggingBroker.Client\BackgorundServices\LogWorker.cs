﻿using LoggingBroker.Client.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;

namespace LoggingBroker.Client.BackgorundServices
{
    public class LogWorker(
        LogQueueService queue,
        ILogClient client,
        IOptions<LoggingBrokerOptions> options,
        ILogger<LogWorker> logger) : BackgroundService
    {
        private readonly LoggingBrokerOptions _options = options.Value;

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // 1) Retry politikamızı burada tanımlıyoruz:
            var retryPolicy = Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(
                    retryCount: _options.MaxRetryCount,
                    sleepDurationProvider: attempt =>
                        TimeSpan.FromSeconds(Math.Pow(_options.RetryBackoffBaseSeconds, attempt)),
                    onRetry: (ex, delay, attempt, ctx) =>
                        logger.LogWarning(ex,
                            "Retry attempt {Attempt} after {Delay}s for batch send.",
                            attempt, delay.TotalSeconds)
                );

            // 2) PeriodicTimer döng<PERSON>ü
            var timer = new PeriodicTimer(TimeSpan.FromSeconds(_options.SendIntervalSeconds));
            try
            {
                while (await timer.WaitForNextTickAsync(stoppingToken))
                {
                    var batch = new List<LogRequestToApi>(_options.BatchSize);
                    await foreach (var log in queue.ReadAllAsync(stoppingToken))
                    {
                        batch.Add(log);
                        if (batch.Count == _options.BatchSize) break;
                    }

                    if (batch.Count == 0) continue;

                    try
                    {
                        // 3) Retry policy’si ile batch’i gönderiyoruz
                        await retryPolicy.ExecuteAsync(ct =>
                            client.SendBatchAsync(batch, ct),
                            stoppingToken);
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex,
                            "Batch gönderimi {MaxRetryCount} kez denedikten sonra başarısız oldu, kuyruğa geri ekleniyor.",
                            _options.MaxRetryCount);
                        foreach (var log in batch)
                            queue.TryEnqueue(log);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // uygulama kapanıyor, sessizce devam
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            logger.LogInformation("LogWorker durduruluyor, kalan loglar gönderiliyor...");
            queue.Complete();

            var remaining = new List<LogRequestToApi>();
            await foreach (var log in queue.ReadAllAsync(cancellationToken))
                remaining.Add(log);

            if (remaining.Count > 0)
            {
                try
                {
                    await client.SendBatchAsync(remaining, cancellationToken);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Durdurma sırasındaki batch gönderimi başarısız oldu.");
                }
            }

            await base.StopAsync(cancellationToken);
        }
    }
}
