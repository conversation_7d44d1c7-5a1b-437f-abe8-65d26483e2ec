﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Texan.Notification.Infrastructure.Services;
using Texan.Notification.Domain.Settings;
using Texan.Notification.Application.DependencyInjection;
using Texan.Notification.Application.Interfaces;

namespace Texan.Notification.Infrastructure.DependencyInjection
{
    public static class InfrastructureLayerServiceExtensions
    {
        public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<MailSettings>(configuration.GetSection("MailSettings"));
            services.Configure<TwilioSettings>(configuration.GetSection("Twilio"));

            services.AddScoped<IMailService, MailService>();
            services.AddScoped<IPushService, PushService>();
            services.AddDummyOrTwilioSmsService(configuration);

            services.AddApplicationLayer();

            return services;
        }
    }

}