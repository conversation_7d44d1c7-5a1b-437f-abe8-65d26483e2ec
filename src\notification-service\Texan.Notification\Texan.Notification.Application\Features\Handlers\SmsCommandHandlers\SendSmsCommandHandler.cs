﻿using Ardalis.Result;
using MediatR;
using Texan.Notification.Application.Features.Commands.SmsCommands;
using Texan.Notification.Application.Interfaces;
using Texan.Notification.Domain.Entities;

namespace Texan.Notification.Application.Features.Handlers.SmsCommandHandlers
{
    public class SendSmsCommandHandler(ISmsService _smsService)
        : IRequestHandler<SendSmsCommand, Result>
    {
        public async Task<Result> Handle(SendSmsCommand request, CancellationToken cancellationToken)
        {
            var dto = new SmsRequestEntity
            {
                PhoneNumber = request.PhoneNumber,
                Message = request.Message,
                CreatedAt = DateTime.UtcNow
            };

            return await _smsService.SendSmsAsync(dto, cancellationToken);
        }
    }
}