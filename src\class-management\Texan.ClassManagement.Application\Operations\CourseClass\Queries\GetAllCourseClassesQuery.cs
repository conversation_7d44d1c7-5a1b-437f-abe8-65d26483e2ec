﻿using Ardalis.Result;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Application.Operations.CourseClass.Queries
{
    public class GetAllCourseClassesQuery
    {
        public class Request : IRequest<Result<List<CourseClassEntity>>>
        {
        }

        public class Handler(ICrudService crudService) : IRequestHandler<Request, Result<List<CourseClassEntity>>>
        {
            public async Task<Result<List<CourseClassEntity>>> Handle(Request request, CancellationToken cancellationToken)
            {
                var courseClasses = await Task.FromResult(crudService.GetAll<CourseClassEntity>().ToList());
                return Result.Success(courseClasses);
            }
        }
    }
}