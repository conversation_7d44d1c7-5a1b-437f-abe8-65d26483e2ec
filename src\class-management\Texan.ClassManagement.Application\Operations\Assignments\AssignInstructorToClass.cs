﻿using Ardalis.Result;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using SMediator.Core.Abstractions;
using Texan.ClassManagement.Application.Abstractions;
using Texan.ClassManagement.Application.Operations.RoleCheck;
using Texan.ClassManagement.Domain.Entities;

namespace Texan.ClassManagement.Application.Operations.Assignments
{
    public class AssignInstructorToClass
    {
        public class Request : IRequest<Result<string>>
        {
            public string CourseClassId { get; set; } = null!;
            public string InstructorUserId { get; set; } = null!;
        }

        public class RequestValidator : AbstractValidator<Request>
        {
            public RequestValidator()
            {
                RuleFor(r => r.CourseClassId)
                    .NotEmpty().WithMessage("CourseClassId is required.");
                RuleFor(r => r.InstructorUserId)
                    .NotEmpty().WithMessage("InstructorUserId is required.");
            }
        }

        public class Handler(ICrudService crudService, IValidator<Request> validator, IMediator mediator) : IRequestHandler<Request, Result<string>>
        {
            public async Task<Result<string>> Handle(Request request, CancellationToken cancellationToken)
            {
                var validationResult = await validator.ValidateAsync(request, cancellationToken);

                if (!validationResult.IsValid)
                {
                    var validationErrors = validationResult.Errors
                        .Select(x => new ValidationError
                        {
                            Identifier = x.PropertyName,
                            ErrorMessage = x.ErrorMessage
                        });
                    return Result.Invalid(validationErrors);
                }

                var courseClass = await crudService.GetByIdAsync<CourseClassEntity>(request.CourseClassId, cancellationToken);

                if (courseClass is null)
                {
                    return Result.NotFound("Class not found");
                }

                var instructorUser = await crudService.GetByIdAsync<UserEntity>(request.InstructorUserId, cancellationToken);

                if (instructorUser is null)
                {
                    return Result.NotFound("Instructor user not found.");
                }

                var hasInstructorRole = await mediator.Send(new CheckUserRole.Request
                {
                    UserId = request.InstructorUserId,
                    Role = "instructor"
                }, cancellationToken);

                if (!hasInstructorRole.IsSuccess)
                {
                    return Result.Forbidden("User is not an Instructor");
                }

                var participation = await crudService.GetAll<ClassParticipationEntity>()
                    .Where(x => x.CourseClass.Id == request.CourseClassId
                    && x.ClassParticipationType == Domain.Enums.ClassParticipationType.MainInstructor)
                    .FirstOrDefaultAsync(cancellationToken);

                if (participation is not null)
                {
                    return Result.Conflict("An Instructor is already assigned to this class.");
                }

                participation = new ClassParticipationEntity()
                {
                    CourseClassId = request.CourseClassId,
                    UserId = request.InstructorUserId,
                    ClassParticipationType = Domain.Enums.ClassParticipationType.MainInstructor,
                    ParticipationDate = DateTime.UtcNow
                };

                await crudService.AddAsync(participation, cancellationToken);

                var result = Result.Success(participation.Id);

                return result;
            }
        }
    }
}