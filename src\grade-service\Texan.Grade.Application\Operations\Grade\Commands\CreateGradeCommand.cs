namespace Texan.Grade.Application.Operations.Grade.Commands;

public class CreateGradeCommand
{
    public class Request : IRequest<Result<string>>
    {
        public string StudentId { get; set; } = string.Empty;
        public Guid AssignmentId { get; set; }
        public string GivenByUserId { get; set; } = string.Empty;
        public decimal Grade { get; set; }
        public bool IsFinal { get; set; } = false;
    }

    public class RequestValidator : AbstractValidator<Request>
    {
        public RequestValidator()
        {
            RuleFor(r => r.StudentId)
                .NotEmpty().WithMessage("Student ID is required.");
            
            RuleFor(r => r.AssignmentId)
                .NotEmpty().WithMessage("Assignment ID is required.");
            
            RuleFor(r => r.GivenByUserId)
                .NotEmpty().WithMessage("Instructor ID is required.");
            
            RuleFor(r => r.Grade)
                .GreaterThanOrEqualTo(0).WithMessage("Grade must be greater than or equal to 0.")
                .LessThanOrEqualTo(100).WithMessage("Grade must be less than or equal to 100.");
        }
    }

    public class Handler(ICrudService crudService, IValidator<Request> validator, ILogger<Handler> logger) : IRequestHandler<Request, Result<string>>
    {
        public async Task<Result<string>> Handle(Request request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Creating grade for student {StudentId} on assignment {AssignmentId}", request.StudentId, request.AssignmentId);
            
            var validationError = await ValidationHelper.ValidateAsync(validator, request, cancellationToken);
            if (validationError != null)
            {
                logger.LogWarning("Grade creation failed validation for student {StudentId}", request.StudentId);
                return validationError;
            }
            
            var assignment = await crudService
                .Query<AssignmentEntity>()
                .FirstOrDefaultAsync(a => a.Id == request.AssignmentId, cancellationToken);

            if (assignment is null)
            {
                logger.LogWarning("Assignment not found for grade creation: {AssignmentId}", request.AssignmentId);
                return Result.NotFound("Assignment not found.");
            }

            var existingGrade = await crudService
                .Query<GradeEntity>()
                .FirstOrDefaultAsync(g => g.StudentId == request.StudentId && g.AssignmentId == request.AssignmentId, cancellationToken);

            if (existingGrade is not null)
            {
                logger.LogWarning("Grade already exists for student {StudentId} on assignment {AssignmentId}", request.StudentId, request.AssignmentId);
                var error = new ValidationError
                {
                    Identifier = nameof(request.StudentId),
                    ErrorMessage = "A grade already exists for this student and assignment."
                };
                return Result.Invalid(error);
            }

            var grade = new GradeEntity
            {
                StudentId = request.StudentId,
                AssignmentId = request.AssignmentId,
                GivenByUserId = request.GivenByUserId,
                Grade = request.Grade,
                IsFinal = request.IsFinal
            };

            await crudService.AddAsync(grade, cancellationToken);
            
            logger.LogInformation("Grade created: {GradeId} for student {StudentId}", grade.Id, request.StudentId);
            return Result.Success(grade.Id.ToString());
        }
    }
}