﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using System.Text.Json;

namespace Texan.Common.JsonSerializer;

public static class DependencyInjection
{
    public static IServiceCollection AddSystemTextJsonSerializer(this IServiceCollection services, JsonSerializerOptions? defaultJsonSerializerOptions = null)
    {
        defaultJsonSerializerOptions ??= JsonSerializerOptions.Web;

        services.TryAddSingleton<IJsonSerializer>(provider => new SystemTextJsonSerializer(defaultJsonSerializerOptions));
        return services;
    }
}